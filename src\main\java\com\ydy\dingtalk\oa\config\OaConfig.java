package com.ydy.dingtalk.oa.config;

import com.ydy.dingtalk.oa.entity.OaCalendarRequest;
import com.ydy.dingtalk.oa.entity.PutOaInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import xyz.erupt.core.annotation.EruptScan;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.dingtalk.core.DingPlatformService;
import xyz.erupt.idaas.enums.PlatformType;
import xyz.erupt.idaas.service.IDaasService;
import xyz.erupt.upms.microapp.MicroApp;
import xyz.erupt.upms.microapp.MicroMenu;
import xyz.erupt.upms.microapp.MicroRole;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Configuration
@ComponentScan
@EntityScan
@EruptScan
@Component
@EnableConfigurationProperties
@Slf4j
@MicroApp(name = OaConst.APPNAME, module = OaConst.APPID,
        defaults = @MicroApp.Default(role = OaConst.ROLE_USER),
        roles = {
                @MicroRole(code = OaConst.ROLE_MANAGER, name = "管理员", home = "OaCalendarRequest", sort = 1,
                        menus = {
                                @MicroMenu(code = OaConst.MENU_ROOT),
                                @MicroMenu(erupt = PutOaInfoRequest.class),
                                @MicroMenu(erupt = OaCalendarRequest.class)
                        }),
                @MicroRole(code = OaConst.ROLE_USER, name = "用户", home = "OaCalendarRequest", sort = 2,
                        menus = {
                                @MicroMenu(code = OaConst.MENU_ROOT),
                                @MicroMenu(erupt = OaCalendarRequest.class)
                        })
        })
public class OaConfig implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(OaConfig.class);
    }

    @Resource
    private IDaasService iDaasService;

    @Resource
    private DingPlatformService dingPlatformService;

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder()
                .name("dingtalk-oa")
                .moduleCode(OaConst.APPID)
                .moduleTitle(OaConst.APPNAME)
                .moduleSort(1)
                .autoCreate(true)
                .build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        List<MetaMenu> menus = new ArrayList<>();
        menus.add(MetaMenu.createRootMenu(OaConst.MENU_ROOT, OaConst.APPNAME, "fa fa-briefcase", 1));
        menus.add(MetaMenu.createEruptClassMenu(PutOaInfoRequest.class, menus.get(0), 2));
        menus.add(MetaMenu.createEruptClassMenu(OaCalendarRequest.class, menus.get(0), 3));
        return menus;
    }

    @Override
    public void initFun() {
        iDaasService.register(OaConst.APPID,
                OaConst.APPNAME, PlatformType.DINGTALK_ACCOUNT,
                OaConst.AK, OaConst.SK,
                "https://ding.xmsxb.com/sns/auth/" + OaConst.APPID,
                "/dingdingOA/index.html");
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dingPlatformService.registerDefaultAccessKey(OaConst.AK, OaConst.SK);
    }
}
