package com.ydy.dingtalk.oa.service;

import com.alibaba.fastjson2.JSON;
import com.ydy.dingtalk.oa.entity.AnalyzeOaInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class OaCalendarCacheService {

    private static final String CACHE_KEY_PREFIX = "oa:calendar:temp:";
    private static final String CACHE_DATE_SET = "oa:calendar:dates";
    private static final long EXPIRE_TIME = 24; // 过期时间（小时）

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存单条日程数据
     */
    public void cacheCalendarData(String date, AnalyzeOaInfoRequest request) {
        try {
            String key = CACHE_KEY_PREFIX + date;
            // 将数据添加到List中
            redisTemplate.opsForList().rightPush(key, JSON.toJSONString(request));
            // 记录日期
            redisTemplate.opsForSet().add(CACHE_DATE_SET, date);
            // 设置过期时间
            redisTemplate.expire(key, EXPIRE_TIME, TimeUnit.HOURS);
            // log.info("缓存日程数据成功, date: {}", date);
        } catch (Exception e) {
            log.error("缓存日程数据失败: {}", e.getMessage());
            throw new RuntimeException("缓存数据失败", e);
        }
    }

    /**
     * 获取某天的所有缓存数据
     */
    public List<Object> getCalendarDataByDate(String date) {
        String key = CACHE_KEY_PREFIX + date;
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 获取所有待处理的日期
     */
    public Set<Object> getAllPendingDates() {
        return redisTemplate.opsForSet().members(CACHE_DATE_SET);
    }

    /**
     * 清除某天的缓存数据
     */
    public void clearCache(String date) {
        String key = CACHE_KEY_PREFIX + date;
        redisTemplate.delete(key);
        redisTemplate.opsForSet().remove(CACHE_DATE_SET, date);
        log.info("清除日期{}的缓存数据", date);
    }

    /**
     * 清除所有缓存数据
     */
    public void clearAllCache() {
        Set<Object> dates = getAllPendingDates();
        if (dates != null) {
            for (Object date : dates) {
                clearCache(date.toString());
            }
        }
        redisTemplate.delete(CACHE_DATE_SET);
        log.info("清除所有缓存数据");
    }
}
