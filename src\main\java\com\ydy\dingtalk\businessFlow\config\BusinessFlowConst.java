package com.ydy.dingtalk.businessFlow.config;

import xyz.erupt.annotation.config.Comment;

public interface BusinessFlowConst {

    /**
     * 应用ID，与模块ID相同
     */
    String APPID = "businessFlow";

    String APPNAME = "商机流转管理";

    String AK = "ding4zayknlilkxduqk1";
    String SK = "mAANc56jYYyKQF698k0kZkZT-2Oc44R5ur47kVwf7C2Eg0c0hG6rcD-N2_ScESRT";

    @Comment("电销")
    String ROLE_ONLINE = "onLine";
    @Comment("面销")
    String ROLE_LINEDOWN = "lineDown";
    @Comment("主管")
    String ROLE_MANAGE = "managed";
    @Comment("管理员")
    String ROLE_ADMIN = "admins";

    @Comment("菜单入口")
    String MENU_ROOT = "$" + APPID;

//    @Comment("统计菜单")
//    String MENU_STAT = "WxOutvisitStat";

    @Comment("统计菜单")
    String MENU_STAT = "/#/bi/WxOutvisitStat";
}
