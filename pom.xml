<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>xyz.erupt</groupId>
        <artifactId>erupt</artifactId>
        <version>1.12.10</version>
    </parent>

    <artifactId>ydy-dingtalk</artifactId>
    <version>1.0.5</version>
    <name>ydy-dingtalk</name>
    <description>ydy-dingtalk</description>
    <properties>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>
<!--        测试事件回调-->
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
            <version>1.2.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-upms</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-idaas</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-dingtalk</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-security</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-cloud-server</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-web</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-devtools</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-bi</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.vladmihalcea</groupId>
            <artifactId>hibernate-types-52</artifactId>
            <version>2.21.1</version>
        </dependency>
    </dependencies>

    <build>
        <directory>${project.basedir}/target</directory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.3.2</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}</finalName>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <finalName>application</finalName>
                    <archive>
                        <!-- 生成的jar中，不要包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.ydy.dingtalk.DingtalkApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- lib依赖包输出目录，打包的时候不打进jar包里 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 压缩jar包，打出来的jar中没有了lib文件夹 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.9</version>
                <configuration>
                    <mainClass>com.ydy.dingtalk.DingtalkApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!--<build>
        <directory>${project.basedir}/../target/${project.basedir}</directory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.3.2</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}</finalName>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <finalName>application</finalName>
                    <archive>
                        &lt;!&ndash; 生成的jar中，不要包含pom.xml和pom.properties这两个文件 &ndash;&gt;
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.ydy.dingtalk.DingtalkApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/target/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>

                    </execution>
                </executions>
            </plugin>
            &lt;!&ndash; 压缩jar包，打出来的jar中没有了lib文件夹 &ndash;&gt;
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layout>ZIP</layout>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
            </plugin>
            &lt;!&ndash; screws 生成数据库文档  &ndash;&gt;
            &lt;!&ndash;            <plugin>&ndash;&gt;
            &lt;!&ndash;                <groupId>cn.smallbun.screw</groupId>&ndash;&gt;
            &lt;!&ndash;                <artifactId>screw-maven-plugin</artifactId>&ndash;&gt;
            &lt;!&ndash;                <version>1.0.5</version>&ndash;&gt;
            &lt;!&ndash;                <dependencies>&ndash;&gt;
            &lt;!&ndash;                    &lt;!&ndash; 数据库连接 &ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <dependency>&ndash;&gt;
            &lt;!&ndash;                        <groupId>com.zaxxer</groupId>&ndash;&gt;
            &lt;!&ndash;                        <artifactId>HikariCP</artifactId>&ndash;&gt;
            &lt;!&ndash;                        <version>3.4.5</version>&ndash;&gt;
            &lt;!&ndash;                    </dependency>&ndash;&gt;
            &lt;!&ndash;                    <dependency>&ndash;&gt;
            &lt;!&ndash;                        <groupId>mysql</groupId>&ndash;&gt;
            &lt;!&ndash;                        <artifactId>mysql-connector-java</artifactId>&ndash;&gt;
            &lt;!&ndash;                        <version>8.0.22</version>&ndash;&gt;
            &lt;!&ndash;                    </dependency>&ndash;&gt;
            &lt;!&ndash;                </dependencies>&ndash;&gt;
            &lt;!&ndash;                <configuration>&ndash;&gt;
            &lt;!&ndash;                    &lt;!&ndash; 数据库相关配置 &ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <driverClassName>com.mysql.cj.jdbc.Driver</driverClassName>&ndash;&gt;
            &lt;!&ndash;                    <jdbcUrl>****************************</jdbcUrl>&ndash;&gt;
            &lt;!&ndash;                    <username>root</username>&ndash;&gt;
            &lt;!&ndash;                    <password>1234@abcD</password>&ndash;&gt;
            &lt;!&ndash;                    &lt;!&ndash; screw 配置 &ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <fileType>HTML</fileType>&ndash;&gt;
            &lt;!&ndash;                    <title>erupt数据库文档</title> &lt;!&ndash;标题&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <fileName>erupt-db</fileName> &lt;!&ndash;文档名称 为空时:将采用[数据库名称-描述-版本号]作为文档名称&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <description>数据库文档</description> &lt;!&ndash;描述&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <version>${project.version}</version> &lt;!&ndash;版本&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <openOutputDir>false</openOutputDir> &lt;!&ndash;打开文件输出目录&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                    <produceType>freemarker</produceType> &lt;!&ndash;生成模板&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                </configuration>&ndash;&gt;
            &lt;!&ndash;                <executions>&ndash;&gt;
            &lt;!&ndash;                    <execution>&ndash;&gt;
            &lt;!&ndash;                        <phase>compile</phase>&ndash;&gt;
            &lt;!&ndash;                        <goals>&ndash;&gt;
            &lt;!&ndash;                            <goal>run</goal>&ndash;&gt;
            &lt;!&ndash;                        </goals>&ndash;&gt;
            &lt;!&ndash;                    </execution>&ndash;&gt;
            &lt;!&ndash;                </executions>&ndash;&gt;
            &lt;!&ndash;            </plugin>&ndash;&gt;
        </plugins>
    </build>-->

</project>
