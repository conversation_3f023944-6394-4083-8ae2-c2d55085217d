var e=Object.defineProperty,t=(t,a,s)=>(((t,a,s)=>{a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s})(t,"symbol"!=typeof a?a+"":a,s),s);import{r as a,a as s,g as n,s as i,b as r,o,c as l,d as c,e as d,f as u,w as p,h,i as A,F as g,j as m,n as f,t as w,k as v,l as y,m as k,v as C,p as E,q as I,u as M,x as S,y as B,z as D}from"./index-nEzJHeOG.js";const T=new class{constructor(){t(this,"watermarkDiv",null),t(this,"observer",null),t(this,"resizeListener",null),t(this,"currentOptions",null),t(this,"defaultOptions",{width:200,height:100,fontSize:14,fontFamily:"Arial",color:"#000000",opacity:.15,rotate:-20,zIndex:9999,gapX:80,gapY:80,offsetLeft:0,offsetTop:0})}createWatermarkCanvas(e){const t=document.createElement("canvas"),a=t.getContext("2d");if(!a)return"";const s=window.devicePixelRatio||1,n=(e.width||this.defaultOptions.width)*s,i=(e.height||this.defaultOptions.height)*s;t.width=n,t.height=i,t.style.width=`${n}px`,t.style.height=`${i}px`,a.scale(s,s),a.fillStyle=e.color||this.defaultOptions.color,a.globalAlpha=e.opacity||this.defaultOptions.opacity,a.font=`${e.fontSize||this.defaultOptions.fontSize}px ${e.fontFamily||this.defaultOptions.fontFamily}`,a.textAlign="center",a.textBaseline="middle",a.translate(n/2/s,i/2/s),a.rotate((e.rotate||this.defaultOptions.rotate)*Math.PI/180);const r=(e.fontSize||this.defaultOptions.fontSize)+5,o=-(e.content.length-1)*r/2;return e.content.forEach(((e,t)=>{a.fillText(e,0,o+t*r)})),t.toDataURL()}createWatermarkDiv(e,t){const a=document.createElement("div");a.style.position="fixed",a.style.top="0",a.style.left="0",a.style.width="100%",a.style.height="100%",a.style.pointerEvents="none",a.style.backgroundImage=`url(${e})`,a.style.backgroundRepeat="repeat",a.style.backgroundPosition=`${t.offsetLeft||this.defaultOptions.offsetLeft}px ${t.offsetTop||this.defaultOptions.offsetTop}px`;const s=t.gapX||this.defaultOptions.gapX,n=t.gapY||this.defaultOptions.gapY,i=t.width||this.defaultOptions.width,r=t.height||this.defaultOptions.height;return a.style.backgroundSize=`${i+s}px ${r+n}px`,a.style.zIndex=`${t.zIndex||this.defaultOptions.zIndex}`,a.setAttribute("data-watermark","true"),a}observeWatermark(e){this.observer&&this.observer.disconnect(),this.observer=new MutationObserver((t=>{t.forEach((t=>{const a=t.target;if("attributes"===t.type&&a===e)this.restoreWatermark(e);else if("childList"===t.type){Array.from(t.removedNodes).includes(e)&&document.body.appendChild(e)}}))})),this.observer.observe(document.body,{attributes:!0,childList:!0,subtree:!0,attributeFilter:["style"]})}restoreWatermark(e){const t={position:"fixed",top:"0",left:"0",width:"100%",height:"100%",pointerEvents:"none",zIndex:e.style.zIndex,backgroundRepeat:e.style.backgroundRepeat,backgroundPosition:e.style.backgroundPosition,backgroundSize:e.style.backgroundSize};Object.assign(e.style,t)}setWatermark(e,t={}){this.watermarkDiv&&(document.body.removeChild(this.watermarkDiv),this.watermarkDiv=null),this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null);const a={...this.defaultOptions,...t,content:e};this.currentOptions=a;const s=this.createWatermarkCanvas(a);this.watermarkDiv=this.createWatermarkDiv(s,a),document.body.appendChild(this.watermarkDiv),this.observeWatermark(this.watermarkDiv),this.resizeListener=this.debounce((()=>{this.currentOptions&&this.setWatermark(this.currentOptions.content,this.currentOptions)}),200),window.addEventListener("resize",this.resizeListener)}removeWatermark(){this.watermarkDiv&&(this.observer&&(this.observer.disconnect(),this.observer=null),this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null),document.body.removeChild(this.watermarkDiv),this.watermarkDiv=null,this.currentOptions=null)}debounce(e,t){let a=null;return(...s)=>{null!==a&&clearTimeout(a),a=window.setTimeout((()=>{e(...s)}),t)}}};const b=new class{constructor(){t(this,"accessToken",""),t(this,"tokenExpireTime",0)}async request(e){var t;try{const{url:t,method:s,data:n,needsToken:i=!1,isV1Api:r=!1}=e,o=!1?"https://ding.local.xmsxb.com":window.location.origin,l=await a({url:`${o}${t}`,method:s,header:{"Content-Type":"application/json"},data:n});console.log("响应数据:",l,o);const c=l.data;if(0===c.errcode||!c.errcode)return c;if(40014===c.errcode)return this.accessToken="",this.tokenExpireTime=0,this.request(e);throw new Error(`请求失败: ${c.errmsg} (错误码: ${c.errcode})`)}catch(s){if(console.error("请求错误:",s),null==(t=s.errMsg)?void 0:t.includes("request:fail"))throw new Error("网络请求失败，请检查网络连接");throw s}}async getCurrentUserInfo(e){const t=await this.request({url:`/erupt-api/oa/getUserInfo?code=${e}`,method:"GET",needsToken:!0});return t.data&&this.addWatermark(t.data),t.data}addWatermark(e){const t=[e.name,e.mobile||""].filter(Boolean);if(t.length>0){const e=window.innerWidth,a=375,s=768,n={...(e=>e<=a?{fontSize:10,rotate:-15,width:100,height:60,gapX:40,gapY:40,offsetLeft:8,offsetTop:8}:e<=s?{fontSize:12,rotate:-18,width:150,height:80,gapX:50,gapY:50,offsetLeft:10,offsetTop:10}:{fontSize:16,rotate:-22,width:200,height:100,gapX:100,gapY:100,offsetLeft:20,offsetTop:20})(e),opacity:.12,color:"#888888"};console.log("水印配置:",n,"屏幕宽度:",e),T.setWatermark(t,n)}}},L=class e{constructor(){t(this,"_userInfo",s(null)),t(this,"_isLoggedIn",s(!1));const e=n("userInfo");e&&(this._userInfo.value=JSON.parse(e),this._isLoggedIn.value=!0)}static getInstance(){return e.instance||(e.instance=new e),e.instance}get userInfo(){return this._userInfo}get isLoggedIn(){return this._isLoggedIn}async autoLogin(e){try{const t=await b.getCurrentUserInfo(e);return this._userInfo.value=t,this._isLoggedIn.value=!0,i("userInfo",JSON.stringify(t)),t}catch(t){throw this._isLoggedIn.value=!1,this._userInfo.value=null,r("userInfo"),t}}logout(){this._isLoggedIn.value=!1,this._userInfo.value=null,r("userInfo")}};t(L,"instance");const O=L.getInstance();"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function U(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Q={exports:{}};const x=U(Q.exports=function(){function e(e){var s=[];return e.AMapUI&&s.push(t(e.AMapUI)),e.Loca&&s.push(a(e.Loca)),Promise.all(s)}function t(e){return new Promise((function(t,a){var n=[];if(e.plugins)for(var l=0;l<e.plugins.length;l+=1)-1==i.AMapUI.plugins.indexOf(e.plugins[l])&&n.push(e.plugins[l]);if(r.AMapUI===s.failed)a("前次请求 AMapUI 失败");else if(r.AMapUI===s.notload){r.AMapUI=s.loading,i.AMapUI.version=e.version||i.AMapUI.version,l=i.AMapUI.version;var c=document.body||document.head,d=document.createElement("script");d.type="text/javascript",d.src="https://webapi.amap.com/ui/"+l+"/main.js",d.onerror=function(e){r.AMapUI=s.failed,a("请求 AMapUI 失败")},d.onload=function(){if(r.AMapUI=s.loaded,n.length)window.AMapUI.loadUI(n,(function(){for(var e=0,a=n.length;e<a;e++){var s=n[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}for(t();o.AMapUI.length;)o.AMapUI.splice(0,1)[0]()}));else for(t();o.AMapUI.length;)o.AMapUI.splice(0,1)[0]()},c.appendChild(d)}else r.AMapUI===s.loaded?e.version&&e.version!==i.AMapUI.version?a("不允许多个版本 AMapUI 混用"):n.length?window.AMapUI.loadUI(n,(function(){for(var e=0,a=n.length;e<a;e++){var s=n[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}t()})):t():e.version&&e.version!==i.AMapUI.version?a("不允许多个版本 AMapUI 混用"):o.AMapUI.push((function(e){e?a(e):n.length?window.AMapUI.loadUI(n,(function(){for(var e=0,a=n.length;e<a;e++){var s=n[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}t()})):t()}))}))}function a(e){return new Promise((function(t,a){if(r.Loca===s.failed)a("前次请求 Loca 失败");else if(r.Loca===s.notload){r.Loca=s.loading,i.Loca.version=e.version||i.Loca.version;var n=i.Loca.version,l=i.AMap.version.startsWith("2"),c=n.startsWith("2");if(l&&!c||!l&&c)a("JSAPI 与 Loca 版本不对应！！");else{l=i.key,c=document.body||document.head;var d=document.createElement("script");d.type="text/javascript",d.src="https://webapi.amap.com/loca?v="+n+"&key="+l,d.onerror=function(e){r.Loca=s.failed,a("请求 AMapUI 失败")},d.onload=function(){for(r.Loca=s.loaded,t();o.Loca.length;)o.Loca.splice(0,1)[0]()},c.appendChild(d)}}else r.Loca===s.loaded?e.version&&e.version!==i.Loca.version?a("不允许多个版本 Loca 混用"):t():e.version&&e.version!==i.Loca.version?a("不允许多个版本 Loca 混用"):o.Loca.push((function(e){e?a(e):a()}))}))}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var s,n;(n=s||(s={})).notload="notload",n.loading="loading",n.loaded="loaded",n.failed="failed";var i={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},r={AMap:s.notload,AMapUI:s.notload,Loca:s.notload},o={AMap:[],AMapUI:[],Loca:[]},l=[],c=function(e){"function"==typeof e&&(r.AMap===s.loaded?e(window.AMap):l.push(e))};return{load:function(t){return new Promise((function(a,n){if(r.AMap==s.failed)n("");else if(r.AMap==s.notload){var o=t.key,d=t.version,u=t.plugins;o?(window.AMap&&"lbs.amap.com"!==location.host&&n("禁止多种API加载方式混用"),i.key=o,i.AMap.version=d||i.AMap.version,i.AMap.plugins=u||i.AMap.plugins,r.AMap=s.loading,d=document.body||document.head,window.___onAPILoaded=function(i){if(delete window.___onAPILoaded,i)r.AMap=s.failed,n(i);else for(r.AMap=s.loaded,e(t).then((function(){a(window.AMap)})).catch(n);l.length;)l.splice(0,1)[0]()},(u=document.createElement("script")).type="text/javascript",u.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+i.AMap.version+"&key="+o+"&plugin="+i.AMap.plugins.join(","),u.onerror=function(e){r.AMap=s.failed,n(e)},d.appendChild(u)):n("请填写key")}else if(r.AMap==s.loaded)if(t.key&&t.key!==i.key)n("多个不一致的 key");else if(t.version&&t.version!==i.AMap.version)n("不允许多个版本 JSAPI 混用");else{if(o=[],t.plugins)for(d=0;d<t.plugins.length;d+=1)-1==i.AMap.plugins.indexOf(t.plugins[d])&&o.push(t.plugins[d]);o.length?window.AMap.plugin(o,(function(){e(t).then((function(){a(window.AMap)})).catch(n)})):e(t).then((function(){a(window.AMap)})).catch(n)}else if(t.key&&t.key!==i.key)n("多个不一致的 key");else if(t.version&&t.version!==i.AMap.version)n("不允许多个版本 JSAPI 混用");else{var p=[];if(t.plugins)for(d=0;d<t.plugins.length;d+=1)-1==i.AMap.plugins.indexOf(t.plugins[d])&&p.push(t.plugins[d]);c((function(){p.length?window.AMap.plugin(p,(function(){e(t).then((function(){a(window.AMap)})).catch(n)})):e(t).then((function(){a(window.AMap)})).catch(n)}))}}))},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,i={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},r={AMap:s.notload,AMapUI:s.notload,Loca:s.notload},o={AMap:[],AMapUI:[],Loca:[]}}}}()),H=(e,t)=>{const a=e.__vccOpts||e;for(const[s,n]of t)a[s]=n;return a};const _=H({name:"AddressMap",props:{address:{type:String,required:!0},id:{type:String,required:!0}},data:()=>({map:null,marker:null,geocoder:null,AKID:"916ec5b9379cf8eb9f5c7b3affb41470"}),computed:{mapId(){return`address-map-${this.id.replace(/[^a-zA-Z0-9]/g,"-")}`}},watch:{async id(){if(this.map&&this.geocoder){console.log("地址发生变化:",this.address);try{await this.geocodeAddress(this.address)}catch(e){console.error("地址解析失败:",e)}}}},methods:{async initMap(){try{const e=await x.load({key:this.AKID,version:"2.0",plugins:["AMap.Geocoder","AMap.ToolBar"]});this.map=new e.Map(this.mapId,{zoom:17,viewMode:"2D"}),e.Geocoder?(this.geocoder=new e.Geocoder({city:"天津",radius:1e3}),this.address&&await this.geocodeAddress(this.address)):console.error("Geocoder 插件未能正确加载");const t=new e.ToolBar({position:"RB",offset:new e.Pixel(10,10)});this.map.addControl(t)}catch(e){console.error("地图初始化失败：",e)}},async geocodeAddress(e){return new Promise((async(t,a)=>{if(!this.map||!this.geocoder)return console.log("地图或解析器未初始化"),this.$emit("changeAddress",!1),void a("地图或解析器未初始化");if(!e)return console.log("地址不能为空"),this.$emit("changeAddress",!1),void a("地址不能为空");if((e=e.trim()).length<3)return console.error("地址长度不够"),this.$emit("changeAddress",!1),!1;return/[^a-zA-Z0-9\u4e00-\u9fa5\s]/.test(e)?(console.error("地址包含不允许的字符"),this.$emit("changeAddress",!1),!1):e.includes("附近")?(console.error("地址格式不正确"),this.$emit("changeAddress",!1),!1):(this.$emit("changeAddress",!1),void this.geocoder.getLocation(e,((e,s)=>{if("complete"===e&&"OK"===s.info){const e=s.geocodes[0].location,a=s.geocodes[0].formattedAddress;this.marker&&this.marker.setMap(null),this.marker=new AMap.Marker({position:[e.lng,e.lat],map:this.map,animation:"AMAP_ANIMATION_DROP",title:a}),this.$emit("changeAddress",!0),this.map.setZoomAndCenter(17,[e.lng,e.lat],!1,800),t(s)}else console.log("地址解析失败:",e),this.$emit("changeAddress",!1),a(e)})))}))}},mounted(){this.initMap()},beforeDestroy(){this.map&&this.map.destroy()}},[["render",function(e,t,a,s,n,i){return o(),l("div",{class:"map-container",ref:"mapContainer"},[c("div",{id:i.mapId,class:"map"},null,8,["id"])],512)}],["__scopeId","data-v-8a5e41f3"]]),J=""+new URL("time_icon-B9z5KobG.png",import.meta.url).href,F="data:image/png;base64,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",W=""+new URL("address_icon-CfGqXeD_.png",import.meta.url).href,K=""+new URL("person_icon-DpSlh23T.png",import.meta.url).href,P=""+new URL("desc_icon-202iQQgz.png",import.meta.url).href;const R=H({components:{AddressMap:_},data:()=>({timeSlots:[],weekDays:[],currentWeekOffset:0,events:[],oaSchedule:[],oaCalendar:[],refreshTimer:null,typeList:[],url:window.location.origin,showDetail:!1,currentEvent:{},popupStyle:{left:"0px",top:"0px"},hasAddress:!0,isPermit:!0,eventTarget:null,calendarHeaderHeight:0,companiesCache:{},scaleFactor:1,contentHeight:0}),beforeDestroy(){this.stopAutoRefresh(),document.removeEventListener("click",this.handleClickOutside),window.removeEventListener("resize",this.handleResize)},unmounted(){this.stopAutoRefresh()},computed:{tableStyle(){return{height:`calc(100vh - ${this.calendarHeaderHeight}px - 40px)`}},rowHeight(){const e=(window.innerHeight-this.calendarHeaderHeight-40)/this.timeSlots.length;return Math.max(60,Math.min(120,e))},fontSize(){const e=window.innerHeight;return e<768?11:e>1080?13:12},truncateLength(){const e=window.innerWidth;return e<1024?15:e<1440?25:35}},created(){this.url=window.location.origin,this.timeSlots=[{name:"上午",startHours:5,endHours:12},{name:"下午",startHours:12,endHours:18},{name:"晚上",startHours:18,endHours:23}],this.generateWeekDays(),this.fetchEvents(),this.getIsPermit(),this.refreshTimer=setInterval((()=>this.fetchEvents()),12e4)},methods:{getMultipleCompanies(e){if(this.currentEvent.id&&this.companiesCache[this.currentEvent.id])return this.companiesCache[this.currentEvent.id];let t=[];try{t=JSON.parse(e),t=t.map((e=>({...e,showMap:!1}))),this.currentEvent.id&&(this.companiesCache[this.currentEvent.id]=t)}catch(a){console.error("解析多公司数据失败:",a),t=[]}return t},async getIsPermit(){const e=window.location.search.substr(1).split("&");let t={};for(let a=0;a<e.length;a++){const s=e[a].split("=");t[s[0]]=s[1]}const s=t._token;s&&await a({url:`${this.url}/erupt-api/oa/isPermit`,method:"get",header:{token:s}}).then((e=>{this.isPermit=e.data.data}))},changeAddress(e){this.hasAddress=e,e?this.setPopupStyle(490):this.setPopupStyle(250)},changeAddressMultiple(e,t){if(!this.currentEvent.id||!this.currentEvent.multipleCompanies)return;this.companiesCache[this.currentEvent.id]||this.getMultipleCompanies(this.currentEvent.multipleCompanies);const a=this.companiesCache[this.currentEvent.id];if(a&&a[e]){a[e].showMap=t;const s=a.filter((e=>e.showMap)).length;s>0?this.setPopupStyle(250+240*s):this.setPopupStyle(250),this.$forceUpdate()}},getCurrentDay(e){let t=new Date,a=`${t.getFullYear()}-${t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1}-${t.getDate()<10?"0"+t.getDate():t.getDate()}`;return e.dateTime==a?"current":""},getClassName(e){var t;return(null==(t=this.typeList.find((t=>t.name==e)))?void 0:t.code)||""},getTypeName(e){var t;return(null==(t=this.typeList.find((t=>t.name==e.sign)))?void 0:t.name)||e.oaType},async fetchEvents(){try{await a({url:`${this.url}/erupt-api/oa/queryOaInfo`}).then((e=>{if(this.events=[],e.data.data.oaInfo&&e.data.data.oaInfo.map((e=>{let t=this.getTime(e);this.events.push({...e,...t})})),e.data.data.oaCalendar){this.oaCalendar=[];const t=[...new Set(e.data.data.oaCalendar.map((e=>e.sign)).filter(Boolean))];this.typeList=[{code:"waichu",name:"外出"},{code:"chuchai",name:"出差"}],t.map(((e,t)=>{this.typeList.push({code:`type${t}`,name:e})})),e.data.data.oaCalendar.map((e=>{let t=this.getTime(e);this.oaCalendar.push({...e,...t})}))}this.$nextTick((()=>{this.calculateScale()}))}))}catch(e){console.error("获取事件数据失败:",e)}},getTime(e){let t=e.startTime.split(" ")[0],a=e.endTime.split(" ")[0],s=e.startTime.split(" ")[1],n=e.endTime.split(" ")[1],i=new Date(e.startTime),r=new Date(e.endTime);return{startDay:t,endDay:a,startHours:i.getHours(),endHours:r.getHours(),startTime:s,endTime:n}},formatHour:e=>`${e}:00`,getDateTime(e,t){return this.weekDays[t][e]},generateWeekDays(){const e=["周一","周二","周三","周四","周五","周六","周日"],t=new Date,a=t.getDay(),s=new Date(t),n=a-1;s.setDate(t.getDate()-n+7*this.currentWeekOffset),this.weekDays=Array.from({length:7},((t,a)=>{const n=new Date(s);n.setDate(s.getDate()+a);let i=n.getMonth()+1<10?"0"+(n.getMonth()+1):n.getMonth()+1,r=n.getFullYear(),o=n.getDate()<10?"0"+n.getDate():n.getDate();return{weekday:e[a],date:`${i}/${o}`,dateTime:`${r}-${i}-${o}`}}))},changeWeek(e){this.currentWeekOffset+=e,this.generateWeekDays(),this.$nextTick((()=>{this.calculateScale()}))},goToToday(){this.currentWeekOffset=0,this.generateWeekDays(),this.$nextTick((()=>{this.calculateScale()}))},getEventsForTimeSlot(e,t){let a=t.startHours,s=t.endHours,n=this.weekDays[e].dateTime;return this.events.map((e=>e.startDay==n&&e.endDay==n&&e.startHours>=a&&e.startHours<s?e:[]))},getCalendarForTimeSlot(e,t){let a=t.startHours,s=t.endHours,n=this.weekDays[e].dateTime;return this.oaCalendar.map((e=>e.startDay==n&&e.endDay==n&&e.startHours>=a&&e.startHours<s?e:[]))},stopAutoRefresh(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)},closeDetail(){this.showDetail=!1,this.currentEvent={}},async handleEventClick(e,t){e&&(e.subject||e.submitter)&&(this.eventTarget=t,this.currentEvent.id!==e.id&&(this.companiesCache={}),this.currentEvent=e,e.multipleCompanies?this.setPopupStyle(250):this.setPopupStyle(490),this.isPermit&&(this.showDetail=!0))},setPopupStyle(e){if(window.innerWidth<=768)this.popupStyle={transform:"translateY(0)"};else{const t=this.eventTarget.target.getBoundingClientRect(),a=window.innerHeight,s=e;let n=t.right+10,i=t.top;n+400>window.innerWidth&&(n=t.left-410),i+s>a&&(i=a-s-10),i<10&&(i=10),this.popupStyle={left:`${n}px`,top:`${i}px`}}},truncateText:(e,t)=>e?e.length>t?e.substring(0,t)+"...":e:"",calculateScale(){const e=document.querySelector(".table"),t=document.querySelector(".grid-container");if(!e||!t)return;const a=e.clientHeight;this.contentHeight=t.clientHeight,this.contentHeight>a&&a>0?(this.scaleFactor=Math.max(.8,a/this.contentHeight),t.style.transform=`scale(${this.scaleFactor})`,t.style.transformOrigin="top center",t.style.height=this.contentHeight*this.scaleFactor+"px"):(this.scaleFactor=1,t.style.transform="scale(1)",t.style.height="auto")},handleResize(){var e;this.calendarHeaderHeight=(null==(e=document.querySelector(".calendar-header"))?void 0:e.clientHeight)||0,this.$nextTick((()=>{this.calculateScale()})),this.$forceUpdate()}},mounted(){this.$nextTick((()=>{this.calendarHeaderHeight=document.querySelector(".calendar-header").clientHeight,window.addEventListener("resize",this.handleResize),this.handleResize()}))}},[["render",function(e,t,a,s,n,i){const r=E,S=I,B=M,D=d("AddressMap");return o(),u(r,{class:"calendar-container"},{default:p((()=>[h(r,{class:"calendar-header"},{default:p((()=>[h(r,{class:"header-content"},{default:p((()=>[h(r,{class:"page-title"},{default:p((()=>[A("每周日程 "),(o(!0),l(g,null,m(n.typeList,(e=>(o(),u(r,{class:f(["colorBlock",e.code]),key:e.code},{default:p((()=>[A(w(e.name),1)])),_:2},1032,["class"])))),128))])),_:1}),h(r,{class:"tip"},{default:p((()=>[A(" 公司内部日程安排，请勿拍照外泄 ")])),_:1}),h(r,{class:"control-buttons"},{default:p((()=>[h(S,{class:"control-btn",onClick:t[0]||(t[0]=e=>i.changeWeek(-1))},{default:p((()=>[A("上一周")])),_:1}),h(S,{class:"control-btn today",onClick:i.goToToday},{default:p((()=>[A("今天")])),_:1},8,["onClick"]),h(S,{class:"control-btn",onClick:t[1]||(t[1]=e=>i.changeWeek(1))},{default:p((()=>[A("下一周")])),_:1})])),_:1})])),_:1})])),_:1}),h(r,{class:"table",style:v(i.tableStyle)},{default:p((()=>[c("table",{class:"grid-container"},[c("tr",{class:"week-header"},[c("th",{class:"time-column-header"}),(o(!0),l(g,null,m(n.weekDays,((e,t)=>(o(),l("th",{key:t,class:f(["day-column-header",i.getCurrentDay(e)])},w(e.weekday)+" "+w(e.date),3)))),128))]),(o(!0),l(g,null,m(n.timeSlots,((e,t)=>(o(),l("tr",{key:e,class:"time-row",style:v({height:i.rowHeight+"px"})},[c("td",{class:"timeBlock"},w(e.name),1),(o(!0),l(g,null,m(n.weekDays,((t,a)=>(o(),l("td",{class:f(["timeBlock","timeBlock-first"]),key:a},[c("div",{class:"tdBlock"},[(o(!0),l(g,null,m(i.getEventsForTimeSlot(a,e),(e=>(o(),l("div",{key:e.id,class:f(["event",e.type]),onClick:t=>i.handleEventClick(e,t),style:v({fontSize:i.fontSize+"px"})},[e.submitter?(o(),l("div",{key:0,class:"text"},[c("div",{class:f(["title","外出"==e.oaType?"waichu":"chuchai"])},[A(w(e.submitter)+"："+w(e.startTime)+"~"+w(e.endTime),1),c("br"),A(" "+w(i.truncateText("地址："+(e.destination||e.companyAddress||""),i.truncateLength)),1)],2)])):y("",!0)],14,["onClick"])))),128)),(o(!0),l(g,null,m(i.getCalendarForTimeSlot(a,e),(e=>(o(),l("div",{key:e.id,class:f(["event"]),onClick:t=>i.handleEventClick(e,t),style:v({fontSize:i.fontSize+"px"})},[e.subject?(o(),l("div",{key:0,class:"text"},[c("div",{class:f(["title",i.getClassName(e.sign)])},[A(w(i.truncateText(e.subject,i.truncateLength/2))+"："+w(e.startTime)+"~"+w(e.endTime),1),c("br"),A(" "+w(i.truncateText("地址："+(e.position||""),i.truncateLength/2))+" ",1),e.participants?(o(),l("div",{key:0},w(i.truncateText("参会人："+e.participants,i.truncateLength)),1)):y("",!0)],2)])):y("",!0)],12,["onClick"])))),128))])])))),128))],4)))),128))])])),_:1},8,["style"]),n.showDetail?(o(),u(r,{key:0,class:"event-popup",style:v(n.popupStyle)},{default:p((()=>[h(r,{class:"popup-header"},{default:p((()=>[h(r,{class:"popup-title"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(n.currentEvent.subject||n.currentEvent.oaType+":"+n.currentEvent.submitter),1)])),_:1})])),_:1}),h(r,{class:"popup-actions"},{default:p((()=>[h(B,{class:"action-btn share"},{default:p((()=>[A(w(i.getTypeName(n.currentEvent)),1)])),_:1}),n.currentEvent.department?(o(),u(B,{key:0,class:"action-btn share"},{default:p((()=>[A(w(n.currentEvent.department),1)])),_:1})):y("",!0),h(B,{class:"close-btn",onClick:i.closeDetail},{default:p((()=>[A("×")])),_:1},8,["onClick"])])),_:1})])),_:1}),h(r,{class:"popup-content"},{default:p((()=>[h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:J,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(n.currentEvent.startTime)+" ~ "+w(n.currentEvent.endTime),1)])),_:1})])),_:1})])),_:1}),n.currentEvent.multipleCompanies?(o(),u(r,{key:0,class:"multiple"},{default:p((()=>[(o(!0),l(g,null,m(i.getMultipleCompanies(n.currentEvent.multipleCompanies),((e,t)=>(o(),u(r,{key:t,class:"multiple-item"},{default:p((()=>[h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:F,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(e.name),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:W,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(e.address),1)])),_:2},1024),k(h(D,{onChangeAddress:e=>i.changeAddressMultiple(t,e),id:`${t}-${e.clientName}`,address:e.address,class:"address-map"},null,8,["onChangeAddress","id","address"]),[[C,!1!==e.showMap]])])),_:2},1024)])),_:2},1024),h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:K,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(e.clientPosition)+" "+w(e.clientName),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(o(),u(r,{key:1,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:W,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(n.currentEvent.position||n.currentEvent.destination||n.currentEvent.companyAddress),1)])),_:1}),k(h(D,{onChangeAddress:i.changeAddress,id:n.currentEvent.subject||n.currentEvent.submitter,address:n.currentEvent.position||n.currentEvent.destination||n.currentEvent.companyAddress,class:"address-map"},null,8,["onChangeAddress","id","address"]),[[C,n.hasAddress]])])),_:1})])),_:1})),"日程"==n.currentEvent.oaType?(o(),u(r,{key:2,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:K,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w(n.currentEvent.participants||n.currentEvent.escort||n.currentEvent.companion),1)])),_:1})])),_:1})])),_:1})):y("",!0),n.currentEvent.companyName||n.currentEvent.tripReason?(o(),u(r,{key:3,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:P,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(B,null,{default:p((()=>[A(w("无"!=n.currentEvent.companyName?n.currentEvent.companyName+"——":"")+w(n.currentEvent.tripReason),1)])),_:1})])),_:1})])),_:1})):y("",!0)])),_:1})])),_:1},8,["style"])):y("",!0)])),_:1})}],["__scopeId","data-v-312a80ed"]]);const z=H({components:{AddressMap:_},data:()=>({timeSlots:[{name:"上午",startHours:5,endHours:12},{name:"下午",startHours:12,endHours:18},{name:"晚上",startHours:18,endHours:23}],weekDays:[],currentWeekOffset:0,events:[],oaCalendar:[],refreshTimer:null,typeList:[],url:window.location.origin,showDetail:!1,currentEvent:{},hasAddress:!0,isPermit:!0,selectedDate:"",headerHeight:0,companiesCache:{}}),computed:{listStyle:()=>({overflowY:"auto"})},methods:{async fetchEvents(){try{const e=await a({url:`${this.url}/erupt-api/oa/queryOaInfo`});if(this.events=[],e.data.data.oaInfo&&(this.events=e.data.data.oaInfo.map((e=>({...e,...this.getTime(e)})))),e.data.data.oaCalendar){this.oaCalendar=[];const t=[...new Set(e.data.data.oaCalendar.map((e=>e.sign)).filter(Boolean))];this.typeList=[{code:"waichu",name:"外出"},{code:"chuchai",name:"出差"}],t.forEach(((e,t)=>{this.typeList.push({code:`type${t}`,name:e})})),this.oaCalendar=e.data.data.oaCalendar.map((e=>({...e,...this.getTime(e)})))}}catch(e){console.error("获取事件数据失败:",e)}},getTime(e){let t=e.startTime.split(" ")[0],a=e.endTime.split(" ")[0],s=e.startTime.split(" ")[1],n=e.endTime.split(" ")[1],i=new Date(e.startTime),r=new Date(e.endTime);return{startDay:t,endDay:a,startHours:i.getHours(),endHours:r.getHours(),startTime:s,endTime:n}},generateWeekDays(e=!1){const t=["一","二","三","四","五","六","日"],a=new Date,s=a.getDay()-1,n=new Date(a);n.setDate(a.getDate()-s+7*this.currentWeekOffset),this.weekDays=Array.from({length:7},((s,i)=>{const r=new Date(n);r.setDate(n.getDate()+i);let o=(r.getMonth()+1).toString().padStart(2,"0"),l=r.getFullYear(),c=r.getDate().toString().padStart(2,"0");const d=`${l}-${o}-${c}`,u=`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`;return this.selectedDate&&!e||d===u&&(this.selectedDate=d),{weekday:t[i],date:`${c}`,dateTime:d,isToday:d==u}})),e||(this.selectedDate=this.weekDays[0].dateTime)},selectDate(e){this.selectedDate=e.dateTime},hasList(){let e=this.events.filter((e=>e.startDay===this.selectedDate)),t=this.oaCalendar.filter((e=>e.startDay===this.selectedDate));return e.length>0||t.length>0},getEventsForTimeSlot(e){return this.events.filter((t=>t.startDay===this.selectedDate&&t.startHours>=e.startHours&&t.startHours<e.endHours))},getCalendarForTimeSlot(e){return this.oaCalendar.filter((t=>t.startDay===this.selectedDate&&t.endDay===this.selectedDate&&t.startHours>=e.startHours&&t.startHours<e.endHours))},getCurrentDay(e){const t=new Date,a=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`;return e.dateTime===a?"current":""},changeWeek(e){this.currentWeekOffset+=e,this.generateWeekDays()},goToToday(){this.currentWeekOffset=0,this.generateWeekDays(!0)},handleEventClick(e,t){e&&(e.subject||e.submitter)&&(this.currentEvent.id!==e.id&&(this.companiesCache={}),this.currentEvent=e,this.isPermit&&(this.showDetail=!0))},closeDetail(){this.showDetail=!1,this.currentEvent={}},changeAddress(e){this.hasAddress=e},getClassName(e){var t;return(null==(t=this.typeList.find((t=>t.name===e)))?void 0:t.code)||""},getTypeName(e){var t;return(null==(t=this.typeList.find((t=>t.name===e.sign)))?void 0:t.name)||e.oaType},getMultipleCompanies(e){if(this.currentEvent.id&&this.companiesCache[this.currentEvent.id])return this.companiesCache[this.currentEvent.id];let t=[];try{t=JSON.parse(e);const a=t.length;t=t.map((e=>({...e,showMap:!1,expanded:1===a}))),this.currentEvent.id&&(this.companiesCache[this.currentEvent.id]=t)}catch(a){console.error("解析多公司数据失败:",a),t=[]}return t},changeAddressMultiple(e,t){if(!this.currentEvent.id||!this.currentEvent.multipleCompanies)return;this.companiesCache[this.currentEvent.id]||this.getMultipleCompanies(this.currentEvent.multipleCompanies);const a=this.companiesCache[this.currentEvent.id];a&&a[e]&&(a[e].showMap=t,this.$forceUpdate())},toggleExpand(e){if(!this.currentEvent.id||!this.currentEvent.multipleCompanies)return;this.companiesCache[this.currentEvent.id]||this.getMultipleCompanies(this.currentEvent.multipleCompanies);const t=this.companiesCache[this.currentEvent.id];if(t&&t[e]){t.length>1?t[e].expanded?(t[e].expanded=!1,t[e].showMap=!1):(t.forEach((e=>{e.expanded=!1,e.showMap=!1})),t[e].expanded=!0):(t[e].expanded=!t[e].expanded,t[e].expanded||(t[e].showMap=!1)),this.$forceUpdate()}}},created(){this.generateWeekDays(!0),this.fetchEvents(),this.refreshTimer=setInterval((()=>this.fetchEvents()),12e4)},mounted(){this.$nextTick((()=>{const e=document.querySelector(".calendar-header");e&&(this.headerHeight=e.offsetHeight)}))},beforeDestroy(){this.refreshTimer&&clearInterval(this.refreshTimer)}},[["render",function(e,t,a,s,n,i){const r=E,v=M,S=I,B=d("AddressMap");return o(),u(r,{class:"mobile-calendar"},{default:p((()=>[h(r,{class:"calendar-header"},{default:p((()=>[h(r,{class:"header-content"},{default:p((()=>[h(r,{class:"page-title"},{default:p((()=>[h(r,{class:"text"},{default:p((()=>[A("每周日程")])),_:1}),h(r,{class:"page-title-text"},{default:p((()=>[(o(!0),l(g,null,m(n.typeList,(e=>(o(),u(r,{class:f(["colorBlock",e.code]),key:e.code},{default:p((()=>[A(w(e.name),1)])),_:2},1032,["class"])))),128))])),_:1})])),_:1}),h(r,{class:"current-date"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(n.selectedDate),1)])),_:1}),h(r,{class:"control-buttons"},{default:p((()=>[h(S,{class:"control-btn",onClick:t[0]||(t[0]=e=>i.changeWeek(-1))},{default:p((()=>[A("上一周")])),_:1}),h(S,{class:"control-btn today",onClick:i.goToToday},{default:p((()=>[A("今天")])),_:1},8,["onClick"]),h(S,{class:"control-btn",onClick:t[1]||(t[1]=e=>i.changeWeek(1))},{default:p((()=>[A("下一周")])),_:1})])),_:1})])),_:1})])),_:1}),h(r,{class:"date-picker"},{default:p((()=>[(o(!0),l(g,null,m(n.weekDays,((e,t)=>(o(),u(r,{key:t,class:f(["date-item",{selected:e.dateTime===n.selectedDate},i.getCurrentDay(e)]),onClick:t=>i.selectDate(e)},{default:p((()=>[h(v,{class:"weekday"},{default:p((()=>[A(w(e.weekday),1)])),_:2},1024),h(v,{class:"date"},{default:p((()=>[A(w(e.isToday?"今":e.date),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),h(r,{class:"schedule-list"},{default:p((()=>[i.hasList()?(o(),u(r,{key:0},{default:p((()=>[(o(!0),l(g,null,m(n.timeSlots,(e=>(o(),u(r,{key:e.name,class:"time-section"},{default:p((()=>[i.getEventsForTimeSlot(e).length>0||i.getCalendarForTimeSlot(e).length>0?(o(),u(r,{key:0,class:"time-label"},{default:p((()=>[A(w(e.name),1)])),_:2},1024)):y("",!0),h(r,{class:"events-container"},{default:p((()=>[(o(!0),l(g,null,m(i.getEventsForTimeSlot(e),(e=>(o(),u(r,{key:e.id,class:f(["event-card","外出"==e.oaType?"waichu":"chuchai"]),onClick:t=>i.handleEventClick(e,t)},{default:p((()=>[h(r,{class:"event-time"},{default:p((()=>[A(w(e.startTime)+" - "+w(e.endTime),1)])),_:2},1024),h(r,{class:"event-title"},{default:p((()=>[A(w(e.submitter),1)])),_:2},1024),h(r,{class:"event-location"},{default:p((()=>[A(w(e.destination||e.companyAddress),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),(o(!0),l(g,null,m(i.getCalendarForTimeSlot(e),(e=>(o(),u(r,{key:e.id,class:f(["event-card",i.getClassName(e.sign)]),onClick:t=>i.handleEventClick(e,t)},{default:p((()=>[h(r,{class:"event-time"},{default:p((()=>[A(w(e.startTime)+" - "+w(e.endTime),1)])),_:2},1024),h(r,{class:"event-title"},{default:p((()=>[A(w(e.subject),1)])),_:2},1024),h(r,{class:"event-location"},{default:p((()=>[A(w(e.position),1)])),_:2},1024),e.participants?(o(),u(r,{key:0,class:"event-participants"},{default:p((()=>[A(" 参会人："+w(e.participants),1)])),_:2},1024)):y("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(o(),u(r,{key:1,class:"empty"},{default:p((()=>[A(" 暂无日程 ")])),_:1}))])),_:1}),n.showDetail?(o(),u(r,{key:0,class:"event-popup"},{default:p((()=>[h(r,{class:"popup-header"},{default:p((()=>[h(r,{class:"popup-title"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(n.currentEvent.subject||n.currentEvent.oaType+":"+n.currentEvent.submitter),1)])),_:1})])),_:1}),h(r,{class:"popup-actions"},{default:p((()=>[h(v,{class:"action-btn share"},{default:p((()=>[A(w(i.getTypeName(n.currentEvent)),1)])),_:1}),n.currentEvent.department?(o(),u(v,{key:0,class:"action-btn share"},{default:p((()=>[A(w(n.currentEvent.department),1)])),_:1})):y("",!0),h(v,{class:"close-btn",onClick:i.closeDetail},{default:p((()=>[A("×")])),_:1},8,["onClick"])])),_:1})])),_:1}),h(r,{class:"popup-content"},{default:p((()=>[h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:J,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(n.currentEvent.startTime)+" ~ "+w(n.currentEvent.endTime),1)])),_:1})])),_:1})])),_:1}),n.currentEvent.multipleCompanies?(o(),u(r,{key:0,class:"multiple"},{default:p((()=>[(o(!0),l(g,null,m(i.getMultipleCompanies(n.currentEvent.multipleCompanies),((e,t)=>(o(),u(r,{key:t,class:"multiple-item"},{default:p((()=>[h(r,{class:"company-header",onClick:e=>i.toggleExpand(t)},{default:p((()=>[h(r,{class:"company-title"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:F,alt:""})])),_:1}),h(v,null,{default:p((()=>[A(w(e.name),1)])),_:2},1024)])),_:2},1024),h(r,{class:f(["expand-icon",e.expanded?"expanded":""])},{default:p((()=>[c("img",{src:"data:image/png;base64,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",alt:"展开",class:"arrow-icon"})])),_:2},1032,["class"])])),_:2},1032,["onClick"]),e.expanded?(o(),u(r,{key:0,class:"company-details"},{default:p((()=>[h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:W,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(e.address),1)])),_:2},1024),k(h(B,{onChangeAddress:e=>i.changeAddressMultiple(t,e),id:`mobile-${t}-${e.clientName}`,address:e.address,class:"address-map"},null,8,["onChangeAddress","id","address"]),[[C,!1!==e.showMap]])])),_:2},1024)])),_:2},1024),h(r,{class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:K,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(e.clientPosition)+" "+w(e.clientName),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)):y("",!0)])),_:2},1024)))),128))])),_:1})):(o(),u(r,{key:1,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:W,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(n.currentEvent.position||n.currentEvent.destination||n.currentEvent.companyAddress),1)])),_:1}),k(h(B,{onChangeAddress:i.changeAddress,id:n.currentEvent.subject||n.currentEvent.submitter,address:n.currentEvent.position||n.currentEvent.destination||n.currentEvent.companyAddress,class:"address-map"},null,8,["onChangeAddress","id","address"]),[[C,n.hasAddress]])])),_:1})])),_:1})),"日程"==n.currentEvent.oaType?(o(),u(r,{key:2,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:K,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w(n.currentEvent.participants||n.currentEvent.escort||n.currentEvent.companion),1)])),_:1})])),_:1})])),_:1})):y("",!0),n.currentEvent.companyName||n.currentEvent.tripReason?(o(),u(r,{key:3,class:"popup-item"},{default:p((()=>[h(r,{class:"item-icon"},{default:p((()=>[c("img",{src:P,alt:""})])),_:1}),h(r,{class:"item-content"},{default:p((()=>[h(v,null,{default:p((()=>[A(w("无"!=n.currentEvent.companyName?n.currentEvent.companyName+"——":"")+w(n.currentEvent.tripReason),1)])),_:1})])),_:1})])),_:1})):y("",!0)])),_:1})])),_:1})):y("",!0)])),_:1})}],["__scopeId","data-v-cd032029"]]),N="ding2bf4796f9eb161d2",G=H(S({__name:"index",setup(e){const t=s(!1),a=s(!1),n=s(!1),i=async e=>{if(n.value){t.value=!0;try{await O.autoLogin(e)}catch(a){D({title:"登录失败",icon:"none"}),console.error(a)}finally{t.value=!1}}else D({title:"请在钉钉中打开",icon:"none"})};return B((()=>{a.value=window.innerWidth<768,(async()=>{try{if(!window.dd)return void(n.value=!1);await new Promise(((e,t)=>{const a=setTimeout((()=>{t(new Error("钉钉 JSAPI 加载超时"))}),1e4);window.dd.ready((()=>{console.log("钉钉 JSAPI 已准备就绪"),clearTimeout(a),e()}))})),await new Promise(((e,t)=>{window.dd.runtime.permission.requestAuthCode({corpId:N,onSuccess:async function(t){console.log(t),n.value=!0,await i(t.code),e()},onFail:function(e){console.error("获取授权码失败:",e),n.value=!1,t(e)}})}))}catch(e){n.value=!1}})()})),(e,t)=>{const s=E;return o(),u(s,{class:"container"},{default:p((()=>[a.value?(o(),u(z,{key:1})):(o(),u(R,{key:0}))])),_:1})}}}),[["__scopeId","data-v-ed9473db"]]);export{G as default};
