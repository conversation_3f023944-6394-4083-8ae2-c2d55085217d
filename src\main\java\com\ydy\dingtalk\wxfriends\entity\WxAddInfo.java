package com.ydy.dingtalk.wxfriends.entity;

import com.ydy.dingtalk.wxfriends.handler.IsReadOnly;
import com.ydy.dingtalk.wxfriends.proxy.WxAddInfoDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Erupt(
        name = "新增数量管理",
        power = @Power(importable = false, export = true),
        importTruncate = true,
        dataProxy = WxAddInfoDataProxy.class,
        orderBy = "WxAddInfo.date desc"
)
@Table(name = "tb_wx_add")
@Entity
@Getter
@Setter
@Comment("新增数量管理")
@ApiModel("新增数量管理")
public class WxAddInfo extends MetaModel {

    @EruptField(views = @View(title = "团队"),
            edit = @Edit(
                    title = "团队",
                    type = EditType.CHOICE,
                    readonly = @Readonly(exprHandler = IsReadOnly.class),
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = "SELECT `id`,`name` FROM e_upms_org WHERE\n" +
                                    "parent_org_id IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t) OR `id` IN (\n" +
                                    "\t\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t)\n" +
                                    "\t\n"
                    )
            )
    )
    @Comment("团队")
    @ApiModelProperty("团队")
    private String subDeptId;

    @EruptField(views = @View(title = "使用人"),
            edit = @Edit(title = "使用人", type = EditType.CHOICE,
                    readonly = @Readonly(exprHandler = IsReadOnly.class),
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = "SELECT e_upms_user.`name`,e_upms_user.`name` FROM e_upms_user INNER JOIN\n" +
                                    "e_upms_org ON\te_upms_user.erupt_org_id=e_upms_org.id\n" +
                                    "WHERE e_upms_org.parent_org_id IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t) OR e_upms_org.`id` IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t)"
                    )
            )
    )
    @Comment("使用人")
    @ApiModelProperty("使用人")
    private String currUser;

    @EruptField(
            views = @View(title = "年份"),
            edit = @Edit(title = "年份", type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.YEAR)))
    @Comment("年份")
    @ApiModelProperty("年份")
    private String year;

    @EruptField(
            views = @View(title = "日期"),
            edit = @Edit(title = "日期", type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            ))
    @Comment("日期")
    @ApiModelProperty("日期")
    private String date;

    @EruptField(
            views = @View(title = "周数"),
            edit = @Edit(title = "周数", type = EditType.INPUT,
                    inputType = @InputType(),
                    search = @Search(vague = true)
            )
    )
    @Comment("周数")
    @ApiModelProperty("周数")
    private String week;

    @EruptField(
            views = @View(title = "微信号选择(手机号)",show = false),
            edit = @Edit(title = "微信号选择(手机号)", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(
                                fullSpan = true,
                                fetchHandler = RemoteCallChoiceFetchHandler.class,
                                fetchHandlerParams = {"main", "erupt-api/get/wxInfoPhone"}
                            )
                    )
            )
    @Comment("微信号选择(手机号)")
    @ApiModelProperty("微信号选择(手机号)")
    private String wxMobile;

    @EruptField(
            views = @View(title = "昵称"),
            edit = @Edit(title = "昵称", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("昵称")
    @ApiModelProperty("昵称")
    private String nickname;

    @EruptField(
            views = @View(title = "微信号"),
            edit = @Edit(title = "微信号", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("微信号")
    @ApiModelProperty("微信号")
    private String wxId;

    @EruptField(
            views = @View(title = "手机号"),
            edit = @Edit(title = "手机号", type = EditType.INPUT,show = false,
                    inputType = @InputType))
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String phone;

    @EruptField(
            views = @View(title = "上次总量"),
            edit = @Edit(title = "上次总量", type = EditType.NUMBER,show = false,
                    numberType = @NumberType(min = 0)))
    @Comment("上次总量")
    @ApiModelProperty("上次总量")
    private Integer lastCount;

    @EruptField(
            views = @View(title = "当前总量"),
            edit = @Edit(title = "当前总量", type = EditType.NUMBER, notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("当前总量")
    @ApiModelProperty("当前总量")
    private Integer currCount;

    @EruptField(
            views = @View(title = "本周新增"),
            edit = @Edit(title = "本周新增", type = EditType.NUMBER,show = false,
                    numberType = @NumberType(min = 0)))
    @Comment("本周新增")
    @ApiModelProperty("本周新增")
    private Integer thisWeekCount;

}

