package com.ydy.dingtalk.businessFlow.selectdevice;

import org.hibernate.procedure.spi.ParameterRegistrationImplementor;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class BusinessTransferUserSelect implements ChoiceFetchHandler {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        boolean res = true;
        for (EruptRole role : roles) {
            if(role.getName().equals("商机流转管理-主管")){
                res = false;
            }
        }
        String jpql;
        if(!res){
            jpql = "select e_upms_user.name FROM e_upms_user JOIN e_upms_user_role ON e_upms_user.id = e_upms_user_role.user_id JOIN e_upms_role ON e_upms_user_role.role_id = e_upms_role.id WHERE e_upms_role.name = '商机流转管理-面销' AND e_upms_user.erupt_org_id = " + eruptUserService.getCurrentEruptUser().getEruptOrg().getId();
        }else {
            jpql = "select e_upms_user.name FROM e_upms_user JOIN e_upms_user_role ON e_upms_user.id = e_upms_user_role.user_id JOIN e_upms_role ON e_upms_user_role.role_id = e_upms_role.id WHERE e_upms_role.name = '商机流转管理-面销'";
        }
        return getVlModels(list, jpql, eruptDao);
    }
    static List<VLModel> getVlModels(List<VLModel> list, String jpql, EruptDao eruptDao) {
        Query query = eruptDao.getEntityManager().createNativeQuery(jpql);
        List resultList = query.getResultList();
        if (resultList.size()>0){
            for (Object o : resultList) {
                VLModel vlModel = new VLModel();
                vlModel.setLabel(o.toString());
                vlModel.setValue(o.toString());
                list.add(vlModel);
            }
        }
        return list;
    }
}
