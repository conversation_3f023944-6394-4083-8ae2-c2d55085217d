package com.ydy.dingtalk.businessFlow.handler;

import cn.hutool.core.date.DateUtil;
import com.ydy.dingtalk.businessFlow.entity.BusinessOrderFeedBack;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;

import javax.transaction.Transactional;
import java.util.List;


@Component
public class BusinessHandlerToOpen  implements OperationHandler<BusinessTransferOrder, BusinessOrderFeedBack> {
    @Override
    @SneakyThrows
    @Transactional
    public String exec(List<BusinessTransferOrder> data, BusinessOrderFeedBack businessOrderFeedBack, String[] param) {
        BusinessTransferOrder businessTransferOrder = data.get(0);
        if(businessTransferOrder.getBusinessStatus().equals("4")){
            throw new EruptApiErrorTip("已结束，不可反馈!");
        }
        if(businessTransferOrder.getBusinessStatus().equals("1")){
            throw new EruptApiErrorTip("尚未接单，不可进行反馈!");
        }
        if(businessOrderFeedBack.getBusinessOrderFeedback().equals("1")){
            businessTransferOrder.setIsDeal("已成交");
        }else {
            businessTransferOrder.setIsDeal("未成交");
        }
        businessTransferOrder.setBusinessStatus("4");
        businessTransferOrder.setBusinessStatusShow("结束");
        businessTransferOrder.setLineDownDate(DateUtil.now());
        return null;
    }
}
