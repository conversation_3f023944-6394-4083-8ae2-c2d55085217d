package com.ydy.dingtalk.businessFlow.util;

import org.springframework.stereotype.Component;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;

@Component
public class BusinessUtill {
    @Resource
    private EruptUserService eruptUserService;

    public String createByUserName(){
        return eruptUserService.getCurrentEruptUser().getName();
    }

    public String createByUserDepartment(){
        return eruptUserService.getCurrentEruptUser().getEruptOrg().getName();
    }
}
