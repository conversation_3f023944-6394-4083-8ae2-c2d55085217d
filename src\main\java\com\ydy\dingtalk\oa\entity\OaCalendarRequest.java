package com.ydy.dingtalk.oa.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "oa日程管理"
)
@Table(name = "tb_oa_calendar_info")
@Entity
@Getter
@Setter
@Comment("oa日程管理")
@ApiModel("oa日程管理")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OaCalendarRequest extends BaseModel {

    @EruptField(
            views = @View(title = "培训主题"),
            edit = @Edit(title = "培训主题", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("日程主题")
    @ApiModelProperty("日程主题")
    private String subject;

    @EruptField(
            views = @View(title = "培训开始时间"),
            edit = @Edit(title = "培训开始时间", type = EditType.DATE,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("培训开始时间")
    @ApiModelProperty("培训开始时间")
    private String startTime;

    @EruptField(
            views = @View(title = "培训结束时间", show = false),
            edit = @Edit(title = "培训结束时间", type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)))
    @Comment("培训结束时间")
    @ApiModelProperty("培训结束时间")
    private String endTime;

    @EruptField(
            views = @View(title = "日程位置"),
            edit = @Edit(title = "日程位置"))
    @Comment("日程位置")
    @ApiModelProperty("日程位置")
    private String position;

    @EruptField(
            views = @View(title = "培训其他内容"),
            edit = @Edit(title = "培训其他内容"))
    @Comment("培训其他内容")
    @ApiModelProperty("培训其他内容")
    private String others;

    @EruptField(
            views = @View(title = "oa类型"),
            edit = @Edit(title = "oa类型", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("oa类型")
    @ApiModelProperty("oa类型")
    private String oaType;

    @EruptField(
            views = @View(title = "日程标签"),
            edit = @Edit(title = "日程标签", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("日程标签")
    @ApiModelProperty("日程标签")
    private String sign;

    @EruptField(
            views = @View(title = "参与人"),
            edit = @Edit(title = "参与人", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("参与人")
    @ApiModelProperty("参与人")
    private String participants;
}
