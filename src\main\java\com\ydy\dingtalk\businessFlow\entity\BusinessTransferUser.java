package com.ydy.dingtalk.businessFlow.entity;

import com.ydy.dingtalk.businessFlow.fileread.FileReadOnlyUtil;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessChoseYunType;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessTransferUserSelect;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

@Erupt(name = "e_business_transaction_user")
@Getter
@Setter
public class BusinessTransferUser extends BaseModel {
    @EruptField(
            views = @View(title = "面销接收人",show = false),
            edit = @Edit(title = "面销接收人", type = EditType.CHOICE, notNull = true, choiceType = @ChoiceType(
                    fetchHandler = BusinessTransferUserSelect.class,
                    fetchHandlerParams = {"α", "β", "γ"}
            ))
    )
    private String businessOrderFeedback;
}
