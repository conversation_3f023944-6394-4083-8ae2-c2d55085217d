package com.ydy.dingtalk.oa.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DateTimeUtils {

    private static final String TIME_PATTERN_REGEX = "\\d{1,2}月\\d{1,2}日\\s+周.\\s+\\d{1,2}:\\d{2}\\s*-\\s*\\d{1,2}:\\d{2}\\(GMT[+-]\\d+\\)";
    // 跨天日期范围的正则表达式 - 同时支持带GMT和不带GMT的格式
    private static final String DATE_RANGE_PATTERN_REGEX = "\\d{1,2}月\\d{1,2}日\\s+周.\\s+-\\s+\\d{1,2}月\\d{1,2}日\\s+周.(?:\\s*\\(GMT[+-]\\d+\\))?";
    // 直接包含时间的跨天日期范围正则表达式，如"3月11日 08:30 - 3月14日 09:00(GMT+8)"
    private static final String TIME_DATE_RANGE_PATTERN_REGEX = "\\d{1,2}月\\d{1,2}日\\s+\\d{1,2}:\\d{2}\\s*-\\s*\\d{1,2}月\\d{1,2}日\\s+\\d{1,2}:\\d{2}\\(GMT[+-]\\d+\\)";
    
    private static final Pattern DATE_PATTERN = Pattern.compile("(\\d{1,2})月(\\d{1,2})日");
    private static final Pattern TIME_PATTERN = Pattern.compile("(\\d{1,2}:\\d{2}) - (\\d{1,2}:\\d{2})");
    // 跨天日期范围的模式 - 与DATE_RANGE_PATTERN_REGEX保持一致
    private static final Pattern DATE_RANGE_PATTERN = Pattern.compile("(\\d{1,2})月(\\d{1,2})日\\s+周.\\s+-\\s+(\\d{1,2})月(\\d{1,2})日\\s+周.");
    // 直接包含时间的跨天日期范围模式
    private static final Pattern TIME_DATE_RANGE_PATTERN = Pattern.compile("(\\d{1,2})月(\\d{1,2})日\\s+(\\d{1,2}:\\d{2})\\s*-\\s*(\\d{1,2})月(\\d{1,2})日\\s+(\\d{1,2}:\\d{2})");

    @Data
    public static class TimeResult {
        private String startTime;
        private String endTime;
        private String dateStr;  // 格式：yyyy-MM-dd
        // 是否是跨天日期
        private boolean isDateRange = false;
        // 跨天日期的所有天
        private List<TimeResult> dateRangeResults = new ArrayList<>();
    }
    
    /**
     * 解析时间字符串，返回开始时间和结束时间
     * @param timeStr 时间字符串，格式如：2月7日 周三 19:00-20:00(GMT+8)
     * @return TimeResult 包含开始时间和结束时间的对象
     */
    public static TimeResult parseTimeString(String timeStr) {
        try {
            // 检查是否是带时间的跨天日期格式，如"3月11日 08:30 - 3月14日 09:00(GMT+8)"
            if (timeStr.matches(TIME_DATE_RANGE_PATTERN_REGEX)) {
                return parseTimeDateRangeString(timeStr);
            }
            
            // 检查是否是跨天日期范围格式，如"3月27日 周四 - 3月28日 周五"
            if (timeStr.matches(DATE_RANGE_PATTERN_REGEX)) {
                return parseDateRangeString(timeStr);
            }
            
            if (!timeStr.matches(TIME_PATTERN_REGEX)) {
                throw new DateTimeParseException("时间格式不匹配", timeStr, 0);
            }
            
            TimeResult result = new TimeResult();
            
            // 1. 获取当前年份
            int year = LocalDate.now().getYear();
            
            // 2. 提取月日
            Matcher dateMatcher = DATE_PATTERN.matcher(timeStr);
            if (!dateMatcher.find()) {
                throw new DateTimeParseException("日期格式错误，无法解析", timeStr, 0);
            }
            
            String month = String.format("%02d", Integer.parseInt(dateMatcher.group(1)));
            String day = String.format("%02d", Integer.parseInt(dateMatcher.group(2)));
            
            // 设置日期字符串
            result.setDateStr(String.format("%d-%s-%s", year, month, day));
            
            String datePart = year + "年" + month + "月" + day + "日";
            
            // 3. 提取时间
            Matcher timeMatcher = TIME_PATTERN.matcher(timeStr);
            if (!timeMatcher.find()) {
                throw new DateTimeParseException("时间格式错误，无法解析", timeStr, 0);
            }
            
            String startTimeStr = timeMatcher.group(1);
            String endTimeStr = timeMatcher.group(2);
            
            // 4. 解析日期
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            LocalDate eventDate = LocalDate.parse(datePart, dateFormatter);
            
            // 5. 解析时间
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H:mm");
            LocalTime startTime = LocalTime.parse(startTimeStr, timeFormatter);
            LocalTime endTime = LocalTime.parse(endTimeStr, timeFormatter);
            
            // 6. 组合日期和时间
            LocalDateTime startDateTime = eventDate.atTime(startTime);
            LocalDateTime endDateTime = eventDate.atTime(endTime);
            
            // 7. 格式化结果
            DateTimeFormatter fullFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            result.setStartTime(startDateTime.format(fullFormatter));
            result.setEndTime(endDateTime.format(fullFormatter));
            
            return result;
        } catch (Exception e) {
            log.error("解析时间失败: {}", e.getMessage());
            throw new RuntimeException("解析时间失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析直接包含时间的跨天日期范围字符串，如"3月11日 08:30 - 3月14日 09:00(GMT+8)"
     * @param timeStr 时间字符串
     * @return TimeResult 包含多天的开始时间和结束时间的对象
     */
    private static TimeResult parseTimeDateRangeString(String timeStr) {
        try {
            log.info("开始解析带时间的日期范围: {}", timeStr);
            
            TimeResult result = new TimeResult();
            result.setDateRange(true);
            
            // 1. 获取当前年份
            int year = LocalDate.now().getYear();
            log.info("当前年份: {}", year);
            
            // 2. 提取日期和时间
            Matcher matcher = TIME_DATE_RANGE_PATTERN.matcher(timeStr);
            if (!matcher.find()) {
                log.error("带时间的日期范围格式错误，无法匹配模式: {}", timeStr);
                throw new DateTimeParseException("带时间的日期范围格式错误，无法解析", timeStr, 0);
            }
            
            // 第一天的月日和时间
            String firstMonth = String.format("%02d", Integer.parseInt(matcher.group(1)));
            String firstDay = String.format("%02d", Integer.parseInt(matcher.group(2)));
            String firstTimeStr = matcher.group(3);
            
            // 最后一天的月日和时间
            String lastMonth = String.format("%02d", Integer.parseInt(matcher.group(4)));
            String lastDay = String.format("%02d", Integer.parseInt(matcher.group(5)));
            String lastTimeStr = matcher.group(6);
            
            log.info("解析到带时间的日期范围: {}月{}日 {}时 - {}月{}日 {}时", 
                    firstMonth, firstDay, firstTimeStr, lastMonth, lastDay, lastTimeStr);
            
            // 设置第一天的日期字符串
            String firstDateStr = String.format("%d-%s-%s", year, firstMonth, firstDay);
            result.setDateStr(firstDateStr);
            
            // 解析起始日期和结束日期
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate firstDate = LocalDate.parse(firstDateStr, dateFormatter);
            LocalDate lastDate = LocalDate.parse(String.format("%d-%s-%s", year, lastMonth, lastDay), dateFormatter);
            
            // 解析起始时间和结束时间
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H:mm");
            LocalTime firstTime = LocalTime.parse(firstTimeStr, timeFormatter);
            LocalTime lastTime = LocalTime.parse(lastTimeStr, timeFormatter);
            
            log.info("日期解析: 第一天 = {}, 第一天时间 = {}, 最后一天 = {}, 最后一天时间 = {}", 
                    firstDate, firstTime, lastDate, lastTime);
            
            // 计算日期范围内的所有日期
            long daysBetween = ChronoUnit.DAYS.between(firstDate, lastDate) + 1; // 包含首尾日期
            log.info("日期范围内共有 {} 天", daysBetween);
            
            DateTimeFormatter fullFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            
            if (daysBetween == 1) {
                // 如果是同一天，只设置起止时间
                LocalDateTime startDateTime = firstDate.atTime(firstTime);
                LocalDateTime endDateTime = lastDate.atTime(lastTime);
                result.setStartTime(startDateTime.format(fullFormatter));
                result.setEndTime(endDateTime.format(fullFormatter));
                return result;
            }
            
            // 如果跨多天，第一天从指定时间到当天结束（18:00）
            LocalDateTime firstStartDateTime = firstDate.atTime(firstTime);
            LocalDateTime firstEndDateTime = firstDate.atTime(LocalTime.of(18, 0));
            result.setStartTime(firstStartDateTime.format(fullFormatter));
            result.setEndTime(firstEndDateTime.format(fullFormatter));
            
            log.info("第一天时间: 开始={}, 结束={}", result.getStartTime(), result.getEndTime());
            
            // 中间天是全天（9:00-18:00）
            for (int i = 1; i < daysBetween - 1; i++) {
                LocalDate date = firstDate.plusDays(i);
                TimeResult dayResult = new TimeResult();
                
                // 设置日期字符串
                String dateStr = date.format(dateFormatter);
                dayResult.setDateStr(dateStr);
                
                // 设置全天时间
                LocalDateTime dayStartDateTime = date.atTime(LocalTime.of(9, 0));
                LocalDateTime dayEndDateTime = date.atTime(LocalTime.of(18, 0));
                dayResult.setStartTime(dayStartDateTime.format(fullFormatter));
                dayResult.setEndTime(dayEndDateTime.format(fullFormatter));
                
                log.info("中间日期 #{}: {}, 开始={}, 结束={}", 
                        i, dateStr, dayResult.getStartTime(), dayResult.getEndTime());
                
                // 添加到结果列表
                result.getDateRangeResults().add(dayResult);
            }
            
            // 最后一天从当天开始（9:00）到指定时间
            if (daysBetween > 1) {
                TimeResult lastDayResult = new TimeResult();
                String lastDateStr = lastDate.format(dateFormatter);
                lastDayResult.setDateStr(lastDateStr);
                
                LocalDateTime lastStartDateTime = lastDate.atTime(LocalTime.of(9, 0));
                LocalDateTime lastEndDateTime = lastDate.atTime(lastTime);
                lastDayResult.setStartTime(lastStartDateTime.format(fullFormatter));
                lastDayResult.setEndTime(lastEndDateTime.format(fullFormatter));
                
                log.info("最后一天: {}, 开始={}, 结束={}", 
                        lastDateStr, lastDayResult.getStartTime(), lastDayResult.getEndTime());
                
                // 添加到结果列表
                result.getDateRangeResults().add(lastDayResult);
            }
            
            log.info("带时间的日期范围解析完成");
            return result;
        } catch (Exception e) {
            log.error("解析带时间的日期范围失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析带时间的日期范围失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析跨天日期范围字符串，返回多天的时间结果
     * @param timeStr 时间字符串，格式如：3月27日 周四 - 3月28日 周五(GMT+8)
     * @return TimeResult 包含多天的开始时间和结束时间的对象
     */
    private static TimeResult parseDateRangeString(String timeStr) {
        try {
            log.info("开始解析日期范围: {}", timeStr);
            
            TimeResult result = new TimeResult();
            result.setDateRange(true);
            
            // 1. 获取当前年份
            int year = LocalDate.now().getYear();
            log.info("当前年份: {}", year);
            
            // 2. 提取日期范围
            Matcher dateRangeMatcher = DATE_RANGE_PATTERN.matcher(timeStr);
            if (!dateRangeMatcher.find()) {
                log.error("日期范围格式错误，无法匹配模式: {}", timeStr);
                throw new DateTimeParseException("日期范围格式错误，无法解析", timeStr, 0);
            }
            
            // 第一天的月日
            String firstMonth = String.format("%02d", Integer.parseInt(dateRangeMatcher.group(1)));
            String firstDay = String.format("%02d", Integer.parseInt(dateRangeMatcher.group(2)));
            // 最后一天的月日
            String lastMonth = String.format("%02d", Integer.parseInt(dateRangeMatcher.group(3)));
            String lastDay = String.format("%02d", Integer.parseInt(dateRangeMatcher.group(4)));
            
            log.info("解析到日期范围: {}月{}日 - {}月{}日", firstMonth, firstDay, lastMonth, lastDay);
            
            // 设置第一天的日期字符串
            String firstDateStr = String.format("%d-%s-%s", year, firstMonth, firstDay);
            result.setDateStr(firstDateStr);
            
            // 解析起始日期和结束日期
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate firstDate = LocalDate.parse(firstDateStr, dateFormatter);
            LocalDate lastDate = LocalDate.parse(String.format("%d-%s-%s", year, lastMonth, lastDay), dateFormatter);
            
            log.info("日期解析: 第一天 = {}, 最后一天 = {}", firstDate, lastDate);
            
            // 设置全天时间（9:00-18:00）
            LocalTime startTime = LocalTime.of(9, 0);
            LocalTime endTime = LocalTime.of(18, 0);
            
            // 格式化日期时间的格式器
            DateTimeFormatter fullFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            
            // 计算日期范围内的所有日期
            long daysBetween = ChronoUnit.DAYS.between(firstDate, lastDate) + 1; // 包含首尾日期
            log.info("日期范围内共有 {} 天", daysBetween);
            
            List<LocalDate> allDates = new ArrayList<>();
            
            for (int i = 0; i < daysBetween; i++) {
                allDates.add(firstDate.plusDays(i));
            }
            
            // 设置第一天的结果
            LocalDateTime firstStartDateTime = allDates.get(0).atTime(startTime);
            LocalDateTime firstEndDateTime = allDates.get(0).atTime(endTime);
            result.setStartTime(firstStartDateTime.format(fullFormatter));
            result.setEndTime(firstEndDateTime.format(fullFormatter));
            
            log.info("第一天时间: 开始={}, 结束={}", result.getStartTime(), result.getEndTime());
            
            // 为每一天创建一个TimeResult，除了第一天（已经在主结果中）
            for (int i = 1; i < allDates.size(); i++) {
                LocalDate date = allDates.get(i);
                TimeResult dayResult = new TimeResult();
                
                // 设置日期字符串
                String dateStr = date.format(dateFormatter);
                dayResult.setDateStr(dateStr);
                
                // 设置时间
                LocalDateTime dayStartDateTime = date.atTime(startTime);
                LocalDateTime dayEndDateTime = date.atTime(endTime);
                dayResult.setStartTime(dayStartDateTime.format(fullFormatter));
                dayResult.setEndTime(dayEndDateTime.format(fullFormatter));
                
                log.info("额外日期 #{}: {}, 开始={}, 结束={}", 
                         i, dateStr, dayResult.getStartTime(), dayResult.getEndTime());
                
                // 添加到结果列表
                result.getDateRangeResults().add(dayResult);
            }
            
            log.info("日期范围解析完成");
            return result;
        } catch (Exception e) {
            log.error("解析日期范围失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析日期范围失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查字符串是否符合时间格式
     * @param timeStr 时间字符串
     * @return boolean
     */
    public static boolean isValidTimeString(String timeStr) {
        return timeStr != null && (
            timeStr.matches(TIME_PATTERN_REGEX) || 
            timeStr.matches(DATE_RANGE_PATTERN_REGEX) ||
            timeStr.matches(TIME_DATE_RANGE_PATTERN_REGEX)
        );
    }
    
    /**
     * 测试日期格式是否匹配
     * 注意：此方法仅用于开发调试，生产环境可删除
     * @param timeStr 要测试的时间字符串
     * @return 包含匹配结果和详细信息的字符串
     */
    public static String testDateFormat(String timeStr) {
        StringBuilder result = new StringBuilder();
        
        result.append("测试字符串: ").append(timeStr).append("\n");
        
        // 测试是否匹配TIME_PATTERN_REGEX
        boolean matchesTimePattern = timeStr != null && timeStr.matches(TIME_PATTERN_REGEX);
        result.append("匹配TIME_PATTERN_REGEX: ").append(matchesTimePattern).append("\n");
        
        // 测试是否匹配DATE_RANGE_PATTERN_REGEX
        boolean matchesDateRangePattern = timeStr != null && timeStr.matches(DATE_RANGE_PATTERN_REGEX);
        result.append("匹配DATE_RANGE_PATTERN_REGEX: ").append(matchesDateRangePattern).append("\n");
        
        // 测试是否匹配TIME_DATE_RANGE_PATTERN_REGEX
        boolean matchesTimeDateRangePattern = timeStr != null && timeStr.matches(TIME_DATE_RANGE_PATTERN_REGEX);
        result.append("匹配TIME_DATE_RANGE_PATTERN_REGEX: ").append(matchesTimeDateRangePattern).append("\n");
        
        // 测试DATE_RANGE_PATTERN
        if (timeStr != null) {
            Matcher dateRangeMatcher = DATE_RANGE_PATTERN.matcher(timeStr);
            boolean dateRangeFound = dateRangeMatcher.find();
            result.append("匹配DATE_RANGE_PATTERN: ").append(dateRangeFound).append("\n");
            
            if (dateRangeFound) {
                result.append("  第一天月份: ").append(dateRangeMatcher.group(1)).append("\n");
                result.append("  第一天日期: ").append(dateRangeMatcher.group(2)).append("\n");
                result.append("  第二天月份: ").append(dateRangeMatcher.group(3)).append("\n");
                result.append("  第二天日期: ").append(dateRangeMatcher.group(4)).append("\n");
            }
        }
        
        // 测试TIME_DATE_RANGE_PATTERN
        if (timeStr != null) {
            Matcher timeDateRangeMatcher = TIME_DATE_RANGE_PATTERN.matcher(timeStr);
            boolean timeDateRangeFound = timeDateRangeMatcher.find();
            result.append("匹配TIME_DATE_RANGE_PATTERN: ").append(timeDateRangeFound).append("\n");
            
            if (timeDateRangeFound) {
                result.append("  第一天月份: ").append(timeDateRangeMatcher.group(1)).append("\n");
                result.append("  第一天日期: ").append(timeDateRangeMatcher.group(2)).append("\n");
                result.append("  第一天时间: ").append(timeDateRangeMatcher.group(3)).append("\n");
                result.append("  第二天月份: ").append(timeDateRangeMatcher.group(4)).append("\n");
                result.append("  第二天日期: ").append(timeDateRangeMatcher.group(5)).append("\n");
                result.append("  第二天时间: ").append(timeDateRangeMatcher.group(6)).append("\n");
            }
        }
        
        return result.toString();
    }
}
