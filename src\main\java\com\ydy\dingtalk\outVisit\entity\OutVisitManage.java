package com.ydy.dingtalk.outVisit.entity;

import com.ydy.dingtalk.businessFlow.fileread.FileReadOnlyUtil;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessChoseYunType;
import com.ydy.dingtalk.outVisit.handler.OutVisitManageHandler;
import com.ydy.dingtalk.outVisit.proxy.OutVisitManageProxy;
import com.ydy.dingtalk.outVisit.proxy.SelectVisitUser;
import com.ydy.dingtalk.wxfriends.handler.IsReadOnly;
import com.ydy.dingtalk.wxfriends.proxy.WxAddInfoDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.RemoteCallChoiceFetchHandler;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Erupt(
        name = "每日客户跟进",power = @Power(export = true),
        dataProxy = OutVisitManageProxy.class,orderBy = "date DESC",
        rowOperation = {
//                @RowOperation(
//                        title = "测试button",
//                        icon = "fa fa-user-circle-o",
//                        operationHandler = OutVisitManageHandler.class,
//                        mode = RowOperation.Mode.BUTTON
//                )
        }
)
@Table(name = "tb_out_manage")
@Entity
@Getter
@Setter
@Comment("每日客户跟进")
@ApiModel("每日客户跟进")
public class OutVisitManage extends MetaModel {


    @EruptField(
            views = @View(title = "提交人",show = false),
            edit = @Edit(title = "提交人",show = false
    ))
    @Comment("提交人")
    @ApiModelProperty("提交人")
    private Long visitUser;

//    @EruptField(
//            views = @View(title = "提交人"),
//            edit = @Edit(title = "提交人", type = EditType.INPUT,
//                    inputType = @InputType(),show = false,
//                    search = @Search(vague = true)
//            ))
//    @Comment("提交人")
//    @ApiModelProperty("提交人")
//    private String visitUserName;

    @EruptField(views = @View(title = "提交人"),
            edit = @Edit(title = "提交人", type = EditType.CHOICE, notNull = true, search = @Search(), choiceType = @ChoiceType(
                    fetchHandler = SelectVisitUser.class,
                    fetchHandlerParams = {"α", "β", "γ"}
            ))
    )
    @Comment("提交人")
    @ApiModelProperty("提交人")
    private String visitUserName;



    @EruptField(
            views = @View(title = "日期"),
            edit = @Edit(title = "日期", type = EditType.DATE,notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            ))
    @Comment("日期")
    @ApiModelProperty("日期")
    private String date;



    @EruptField(
            views = @View(title = "跟进记录总数"),
            edit = @Edit(title = "跟进记录总数", type = EditType.NUMBER,notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("跟进记录总数")
    @ApiModelProperty("跟进记录总数")
    private String followNum;

    @EruptField(
            views = @View(title = "有效跟进记录数"),
            edit = @Edit(title = "有效跟进记录数", type = EditType.NUMBER,notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("有效跟进记录数")
    @ApiModelProperty("有效跟进记录数")
    private String effectiveFollowNum;

    @EruptField(
            views = @View(title = "添加微信好友数"),
            edit = @Edit(title = "添加微信好友数", type = EditType.NUMBER,notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("添加微信好友数")
    @ApiModelProperty("添加微信好友数")
    private String addWxFriend;

    @EruptField(
            views = @View(title = "新增商机数"),
            edit = @Edit(title = "新增商机数", type = EditType.NUMBER,notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("新增商机数")
    @ApiModelProperty("新增商机数")
    private String addBusinessOpportunities;

    @EruptField(
            views = @View(title = "外出拜访客户数"),
            edit = @Edit(title = "外出拜访客户数", type = EditType.NUMBER,notNull = true,
                    numberType = @NumberType(min = 0)))
    @Comment("外出拜访客户数")
    @ApiModelProperty("外出拜访客户数")
    private String visitCustomersNum;

    @EruptField(
            views = @View(title = "今日有效业务金额"),
            edit = @Edit(title = "今日有效业务金额", type = EditType.NUMBER,notNull = true,
                    inputType = @InputType()))
    @Comment("今日有效业务金额")
    @ApiModelProperty("今日有效业务金额")
    private BigDecimal effectiveBusinessAmount;


    @EruptField(
            views = @View(title = "重点客户名称（填写客户CRM名称）"),
            edit = @Edit(title = "重点客户名称（填写客户CRM名称）", type = EditType.INPUT,
                    inputType = @InputType()
            ))
    @Comment("重点客户名称（填写客户CRM名称）")
    @ApiModelProperty("重点客户名称（填写客户CRM名称）")
    private String keyCustomer;

}

