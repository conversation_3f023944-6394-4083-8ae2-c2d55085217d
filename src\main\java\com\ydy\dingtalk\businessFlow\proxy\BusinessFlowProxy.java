package com.ydy.dingtalk.businessFlow.proxy;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;


import com.ydy.dingtalk.businessFlow.handler.BusiniessAgainTransfer;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.pqc.crypto.newhope.NHOtherInfoGenerator;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.QueryExpression;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.view.EruptFieldModel;
import xyz.erupt.core.view.EruptModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since :2023/10/11:9:29
 */
@Service
public class BusinessFlowProxy implements DataProxy<BusinessTransferOrder> {

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private BusiniessAgainTransfer businiessAgainTransfer;

    @Override
    public void addBehavior(BusinessTransferOrder outVisitManage) {
       outVisitManage.setVisitUser(eruptUserService.getCurrentEruptUser().getId().toString());
       outVisitManage.setPhoneTransferDepartment(eruptUserService.getCurrentEruptUser().getEruptOrg().getName());
       outVisitManage.setPhoneTransferName(eruptUserService.getCurrentEruptUser().getName());
    }

    @Override
    public void beforeUpdate(BusinessTransferOrder outVisitManage){
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Set<EruptRole> roles = currentEruptUser.getRoles();
        for(EruptRole eruptRole : roles) {
            if (eruptRole.getName().equals("商机流转管理-电销") && !(outVisitManage.getBusinessStatus().equals("1"))) {
                throw new EruptApiErrorTip("电销人员提交后不可修改!");
            }
            if(eruptRole.getName().equals("商机流转管理-面销")){
                if(outVisitManage.getBusinessStatus().equals("4")){
                    throw new EruptApiErrorTip("已结束，不可进行操作!");
                }
                outVisitManage.setLineDownDate(DateUtil.now());
                // 加上校验，如果是面销的编辑时，以下四个作为必填项
                EruptModel eruptModel = EruptCoreService.getErupt(outVisitManage.getClass().getSimpleName());
                Set<String> requiredFields = new HashSet<>(Arrays.asList("客户类型", "客户评估结果", "半年内是否有采购需求", "是否拜访"));

                Map<String, Supplier<String>> fieldCheckMap = new HashMap<>();
                fieldCheckMap.put("客户类型", outVisitManage::getCustomerType);
                fieldCheckMap.put("客户评估结果", outVisitManage::getCustomerAssessmentResult);
                fieldCheckMap.put("半年内是否有采购需求", outVisitManage::getHalfYearPurchaseDemand);
                fieldCheckMap.put("是否拜访", outVisitManage::getIsVisit);

                for (EruptFieldModel eruptFieldModel : eruptModel.getEruptFieldModels()) {
                    String title = eruptFieldModel.getEruptField().edit().title();
                    if (requiredFields.contains(title) && StringUtils.isEmpty(fieldCheckMap.get(title).get())) {
                        throw new EruptApiErrorTip("请选择 " + title);
                    }
                }

            }
        }
    }

    @Override
    public void beforeDelete(BusinessTransferOrder outVisitManage) {
        if(!outVisitManage.getBusinessStatus().equals("1")){
            throw new EruptApiErrorTip("提交之后不可删除!");
        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Set<EruptRole> roles = currentEruptUser.getRoles();
        for(EruptRole eruptRole : roles){
            if(eruptRole.getName().equals("商机流转管理-电销")){
                conditions.add(new Condition("visitUser",currentEruptUser.getId().toString(),QueryExpression.EQ));
                break;
            } else if(eruptRole.getName().equals("商机流转管理-面销")){
                conditions.add(new Condition("lineDownUser",currentEruptUser.getId().toString(),QueryExpression.EQ));
                List<String> objects = new ArrayList<>();
                objects.add("2");
                objects.add("4");
                objects.add("3");
                conditions.add(new Condition("businessStatus",objects,QueryExpression.IN));
                break;
            } else if(eruptRole.getName().equals("商机流转管理-主管")){
                List<Long> idList = new ArrayList<>();
                BusinessLineDownProxy.getOrgUserList(eruptUserService, eruptDao,idList);
                conditions.add(new Condition("lineDownUser",idList,QueryExpression.IN));
//                conditions.add(new Condition("visitUser",idList,QueryExpression.IN));
                break;
            }
        }
        return null;
    }

    @Override
    public void beforeAdd(BusinessTransferOrder outVisitManage) {
        outVisitManage.setBusinessStatus("1");
        outVisitManage.setBusinessStatusShow("未转单");
        //设置创建时间
        outVisitManage.setCreateTime(LocalDateTime.now());
        //设置创建人
        outVisitManage.setCreateBy(eruptUserService.getCurrentEruptUser().getName());
        businiessAgainTransfer.getBusinessTransferOrder(outVisitManage);
    }
}
