package com.ydy.dingtalk.oa.service;

import com.alibaba.fastjson2.JSON;
import com.ydy.dingtalk.oa.entity.AnalyzeOaInfoRequest;
import com.ydy.dingtalk.oa.entity.BatchProcessResult;
import com.ydy.dingtalk.oa.entity.OaCalendarRequest;
import com.ydy.dingtalk.oa.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptPlatformService;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OaCalendarService {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptPlatformService eruptPlatformService;

    /**
     * 解析日程信息
     */
    public List<String> parseCalendarSchedule(String calendarSchedule) {
        List<String> list = JSON.parseArray(calendarSchedule, String.class);
        List<String> newList = new ArrayList<>();
        StringBuilder prefix = new StringBuilder();
        boolean foundTime = false;

        for (String item : list) {
            if (!foundTime && DateTimeUtils.isValidTimeString(item)) {
                newList.add(prefix.toString().replaceAll("\\s+", ""));
                newList.add(item);
                foundTime = true;
            } else if (!foundTime) {
                prefix.append(item);
            } else {
                newList.add(item);
            }
        }
        return newList;
    }

    /**
     * 处理参与人信息
     */
    public String processParticipants(String participants) {
        String oaAccount = eruptPlatformService.getOption("OA_ACCOUNT_SIGN").getAsString();
        participants = participants.replace(oaAccount, "");
        return participants.replace("\"", "").replace("[", "").replace("]", "");
    }

    /**
     * 解析主题和标识
     */
    public void parseSubjectAndSign(List<String> list, OaCalendarRequest request) {
        if (list.isEmpty()) {
            return;
        }

        String subject = list.get(0);
        String sign = null;
        String key = eruptPlatformService.getOption("CALENDAR_SIGN").getAsString();
        String[] keywords = key.split(",");

        for (String keyword : keywords) {
            if (subject.contains(keyword)) {
                sign = keyword;
                subject = subject.replace(keyword, "").trim();
                break;
            }
        }

        request.setSubject(subject);
        request.setSign(sign);
    }

    /**
     * 处理位置信息
     */
    public void processLocation(List<String> list, List<String> participantList, OaCalendarRequest request) {
        if (list.size() <= 2) {
            return;
        }

        String location = list.get(2);
        if (isValidLocation(location, participantList)) {
            request.setPosition(location);
        } else {
            request.setPosition("无");
            String others = location;
            if (list.size() > 3) {
                others += ", " + String.join(", ", list.subList(3, list.size()));
            }
            String delContent = eruptPlatformService.getOption("DELETE_CONTENT").getAsString();
            String replace = others.replace(delContent, "");
            request.setOthers(replace);
        }
    }

    /**
     * 验证位置是否有效
     */
    private boolean isValidLocation(String location, List<String> participants) {
        String roomSign = eruptPlatformService.getOption("OA_MEETING_ROOMS").getAsString();
        String[] meetingRooms = roomSign.split(",");

        for (String room : meetingRooms) {
            if (location.contains(room)) {
                return true;
            }
        }

        if (location.contains("茶室")) {
            return true;
        }

        for (String participant : participants) {
            if (location.contains(participant)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查日程是否已存在
     */
    public boolean isCalendarExists(OaCalendarRequest request) {
        OaCalendarRequest existing = eruptDao.queryEntity(OaCalendarRequest.class, "subject = '" + request.getSubject() + "' and start_time = '" + request.getStartTime() + "'");
        return existing != null;
    }

    /**
     * 获取当月日程数据
     */
    public List<OaCalendarRequest> getCurrentMonthData() {
        LocalDate now = LocalDate.now();
        String startDate = now.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endDate = now.withDayOfMonth(now.lengthOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return eruptDao.queryEntityList(OaCalendarRequest.class, "start_time >= '" + startDate + " 00:00' AND start_time <= '" + endDate + " 23:59'");
    }

    /**
     * 删除日程数据
     */
    public void deleteCalendar(OaCalendarRequest calendar) {
        eruptDao.delete(calendar);
    }

    /**
     * 保存日程数据
     */
    public void saveCalendar(OaCalendarRequest calendar) {
        eruptDao.merge(calendar);
    }

    /**
     * 创建日程请求对象
     */
    public OaCalendarRequest createCalendarRequest(AnalyzeOaInfoRequest analyzeRequest) {
        OaCalendarRequest request = new OaCalendarRequest();

        // 处理参与人
        String participants = processParticipants(analyzeRequest.getParticipants());
        request.setParticipants(participants);

        // 解析日程信息
        List<String> calendarList = parseCalendarSchedule(analyzeRequest.getCalendarSchedule());

        // 解析主题和标识
        parseSubjectAndSign(calendarList, request);

        // 设置类型
        request.setOaType("日程");

        // 处理位置
        List<String> participantList = JSON.parseArray(analyzeRequest.getParticipants(), String.class);
        processLocation(calendarList, participantList, request);

        // 处理时间
        if (calendarList.size() > 1) {
            String timeInterval = calendarList.get(1);
            DateTimeUtils.TimeResult timeResult = DateTimeUtils.parseTimeString(timeInterval);
            request.setStartTime(timeResult.getStartTime());
            request.setEndTime(timeResult.getEndTime());
            
            // 检查是否是跨天日期
            if (timeResult.isDateRange() && !timeResult.getDateRangeResults().isEmpty()) {
                // 如果是跨天日期，返回第一天的结果，并保存其他天的数据
                List<DateTimeUtils.TimeResult> additionalDays = timeResult.getDateRangeResults();
                for (DateTimeUtils.TimeResult dayResult : additionalDays) {
                    OaCalendarRequest nextDayRequest = new OaCalendarRequest();
                    // 复制相同的属性
                    nextDayRequest.setParticipants(participants);
                    nextDayRequest.setSubject(request.getSubject());
                    nextDayRequest.setSign(request.getSign());
                    nextDayRequest.setOaType("日程");
                    nextDayRequest.setPosition(request.getPosition()); // 确保复制位置信息
                    nextDayRequest.setOthers(request.getOthers());
                    
                    // 设置当天的时间
                    nextDayRequest.setStartTime(dayResult.getStartTime());
                    nextDayRequest.setEndTime(dayResult.getEndTime());
                    
                    // 确保处理位置字段（虽然已经从request中复制，但为保险起见再次设置）
                    processLocation(calendarList, participantList, nextDayRequest);
                    
                    // 保存当天的数据
                    if (!isCalendarExists(nextDayRequest)) {
                        saveCalendar(nextDayRequest);
                    }
                }
            }
        }

        return request;
    }
}
