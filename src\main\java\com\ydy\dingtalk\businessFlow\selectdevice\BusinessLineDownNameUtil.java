package com.ydy.dingtalk.businessFlow.selectdevice;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.ydy.dingtalk.businessFlow.selectdevice.BusinessChoseYunType.getVlModels;

@Component
public class BusinessLineDownNameUtil implements ChoiceFetchHandler {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;


    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        boolean res = true;
        if(eruptUserService.getCurrentEruptUser().getId() == 1){
            String jpql = "SELECT a.name " +
                    "FROM e_upms_user a " +
                    "JOIN e_upms_user_role b ON a.id = b.user_id " +
                    "JOIN e_upms_role c ON b.role_id = c.id " +
                    "WHERE c.name = '商机流转管理-面销'";
            return getVlModels(list, jpql, eruptDao);
        }
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        for (EruptRole role : roles) {
            if(role.getName().equals("商机流转管理-管理员")){
                String jpql = "SELECT a.name " +
                        "FROM e_upms_user a " +
                        "JOIN e_upms_user_role b ON a.id = b.user_id " +
                        "JOIN e_upms_role c ON b.role_id = c.id " +
                        "WHERE c.name = '商机流转管理-面销'";
                return getVlModels(list, jpql, eruptDao);
            }
            if(role.getName().equals("商机流转管理-主管")){
                res = false;
            }
        }
        if(res){
            list.add(new VLModel(eruptUserService.getCurrentEruptUser().getName(),eruptUserService.getCurrentEruptUser().getName()));
            return list;
        }
        String jpql = "SELECT a.name " +
                "FROM e_upms_user a " +
                "JOIN e_upms_user_role b ON a.id = b.user_id " +
                "JOIN e_upms_role c ON b.role_id = c.id " +
                "WHERE c.name = '商机流转管理-面销' AND a.erupt_org_id = " + eruptUserService.getCurrentEruptUser().getEruptOrg().getId();
        return getVlModels(list, jpql, eruptDao);
    }
}
