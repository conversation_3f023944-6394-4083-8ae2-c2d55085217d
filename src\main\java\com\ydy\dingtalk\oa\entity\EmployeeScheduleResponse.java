package com.ydy.dingtalk.oa.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Employee schedule response entity
 */
@Data
public class EmployeeScheduleResponse {
    /**
     * List of date headers (format: yyyy-MM-dd)
     */
    private List<String> dates;
    
    /**
     * List of employee schedule data
     */
    private List<EmployeeSchedule> schedules;
    
    @Data
    public static class EmployeeSchedule {
        /**
         * Employee name
         */
        private String employeeName;
        
        /**
         * Schedule data for each date, key is the date (yyyy-MM-dd)
         */
        private Map<String, List<ScheduleItem>> dateSchedules;
    }
    
    @Data
    public static class ScheduleItem {
        /**
         * Schedule type: 外出, 出差, 培训交流, 外部参会, 内部会议
         */
        private String type;
        
        /**
         * Time period: 上午, 下午, 全天
         */
        private String timePeriod;
        
        /**
         * Company name or title
         */
        private String content;
    }
} 