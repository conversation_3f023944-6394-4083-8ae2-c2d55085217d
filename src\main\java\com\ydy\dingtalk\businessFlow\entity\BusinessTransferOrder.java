package com.ydy.dingtalk.businessFlow.entity;


import com.ydy.dingtalk.businessFlow.fileread.FileReadOnlyUtil;
import com.ydy.dingtalk.businessFlow.fileread.LineDownLimitEdit;
import com.ydy.dingtalk.businessFlow.handler.*;
import com.ydy.dingtalk.businessFlow.proxy.*;

import com.ydy.dingtalk.businessFlow.selectdevice.BusinessChoseYunType;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessLineDownNameUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Drill;
import xyz.erupt.annotation.sub_erupt.Link;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.bi.model.BiChart;
import xyz.erupt.bi.model.BiHistory;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Erupt(
        name = "商机转单"
        , power = @Power(export = true)
        ,
        rowOperation = {
                @RowOperation(
                        title = "提交",
                        operationHandler = BusinessToSubmit.class,
                        mode = RowOperation.Mode.SINGLE,
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        ifExpr = "item.businessStatus == '1'",
                        show = @ExprBool(exprHandler = BusinessSubmitShow.class, params = "testBtn")
                ),
                @RowOperation(
                        title = "重新分配面销",
                        operationHandler = BusiniessAgainTransfer.class,
                        mode = RowOperation.Mode.SINGLE,
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        ifExpr = "item.businessStatus == '1'",
                        show = @ExprBool(exprHandler = BusinessSubmitShow.class, params = "testBtn")
                ),
//                @RowOperation(
//                        title = "接单",
//                        icon = "fa fa-user-circle-o",
//                        operationHandler = BusinessAccept.class,
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = BusinessControAccept.class, params = "testBtn")
//                ),
//                @RowOperation(
//                        title = "拒单",
//                        icon = "fa fa-user-circle-o",
//                        operationHandler = BusinessRefuse.class,
//                        mode = RowOperation.Mode.SINGLE,
//                        show = @ExprBool(exprHandler = BusinessControAccept.class, params = "testBtn")
//                ),
                @RowOperation(
                        title = "成单反馈",
                        eruptClass = BusinessOrderFeedBack.class,
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = BusinessHandlerToOpen.class,
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        ifExpr = "item.businessStatus == '2'",
                        show = @ExprBool(exprHandler = BusinessControAccept.class, params = "testBtn")
                ),
                @RowOperation(
                        title = "商机转移",
                        eruptClass = BusinessTransferUser.class,
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = BusinessToTransferUser.class,
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        ifExpr = "item.businessStatus == '2'",
                        show = @ExprBool(exprHandler = BusenessControlTransferUser.class, params = "{businessStatus}")
                ),

        },
        dataProxy = BusinessFlowProxy.class,
        orderBy = "createTime DESC",
        drills = {
                @Drill(title = "转移记录", icon = "fa fa-pie-chart"
                        , link = @Link(linkErupt = BusinessTransferRecord.class, joinColumn = "businessId"), show = @ExprBool(exprHandler = BusinessShowLastTransferList.class, params = "testBtn")),
        }
)
@Table(name = "e_business_trans_list")
@Entity
@Getter
@Setter
@Comment("商机转单")
@ApiModel("商机转单")
public class BusinessTransferOrder extends MetaModel {

    @EruptField(
            views = @View(title = "公司名称"),
            edit = @Edit(title = "公司名称", type = EditType.INPUT, notNull = true, readonly = @Readonly(add = false, edit = false, exprHandler = FileReadOnlyUtil.class)))
    @Comment("公司名称")
    @ApiModelProperty("公司名称")
    private String companyName;

    @EruptField(
            views = @View(title = "客户分类"),
            edit = @Edit(title = "客户分类", type = EditType.CHOICE, notNull = true, search = @Search(), choiceType = @ChoiceType(
                    fetchHandler = BusinessChoseYunType.class,
                    fetchHandlerParams = {"α", "β", "γ"}
            ), readonly = @Readonly(add = false, edit = false, exprHandler = FileReadOnlyUtil.class))
    )
    @Comment("客户分类")
    @ApiModelProperty("客户分类")
    private String effectiveFollowNum;

    @EruptField(
            views = @View(title = "CRM客户地址", show = false),
            edit = @Edit(title = "CRM客户地址", type = EditType.INPUT, notNull = true, readonly = @Readonly(add = false, edit = false, exprHandler = FileReadOnlyUtil.class)))
    @Comment("CRM客户地址")
    @ApiModelProperty("CRM客户地址")
    private String crmUserAddress;


    @EruptField(views = @View(title = "电销转单人"),
            edit = @Edit(title = "电销转单人", type = EditType.INPUT, search = @Search(vague = true), readonly = @Readonly())
    )
    @Comment("电销转单人")
    @ApiModelProperty("电销转单人")
    private String phoneTransferName;

    @EruptField(views = @View(title = "电销转单人部门", show = false),
            edit = @Edit(title = "电销转单人部门", type = EditType.INPUT, readonly = @Readonly())
    )
    @Comment("电销转单人部门")
    @ApiModelProperty("电销转单人部门")
    private String phoneTransferDepartment;


    @EruptField(
            views = @View(title = "面销接单人"),
            edit = @Edit(title = "面销接单人", type = EditType.INPUT, search = @Search(vague = true), readonly = @Readonly()))
    @Comment("面销接单人")
    @ApiModelProperty("面销接单人")
    private String lineDownUserName;

    @EruptField(
            views = @View(title = "客户类型"),
            edit = @Edit(title = "客户类型", type = EditType.CHOICE, readonly = @Readonly(add = false, edit = false, exprHandler = LineDownLimitEdit.class), search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "企业", value = "1"),
                            @VL(label = "个人", value = "2"),
                            @VL(label = "待确定", value = "3"),
                    }
            )))
    @Comment("客户类型")
    @ApiModelProperty("客户类型")
    private String customerType;

    @EruptField(
            views = @View(title = "客户评估结果"),
            edit = @Edit(title = "客户评估结果", type = EditType.CHOICE, readonly = @Readonly(add = false, edit = false, exprHandler = LineDownLimitEdit.class), search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "有效客户", value = "1"),
                            @VL(label = "无效客户", value = "2"),
                            @VL(label = "未联系到用户", value = "3"),
                            @VL(label = "待评估", value = "4"),
                    }
            )))

    @Comment("客户评估结果")
    @ApiModelProperty("客户评估结果")
    private String customerAssessmentResult ="4";

    @EruptField(
            views = @View(title = "是否成交", show = false),
            edit = @Edit(title = "是否成交", type = EditType.INPUT, readonly = @Readonly))
    @Comment("是否成交")
    @ApiModelProperty("是否成交")
    private String isDeal;


    @EruptField(
            views = @View(title = "提交人", show = false),
            edit = @Edit(title = "提交人", type = EditType.INPUT,
                    inputType = @InputType(), show = false

            ))
    @Comment("提交人")
    @ApiModelProperty("提交人")
    private String visitUser;


    @EruptField(
            views = @View(title = "面销接单人", show = false))
    @Comment("面销接单人")
    @ApiModelProperty("面销接单人")
    private String lineDownUser;


    @EruptField(
            views = @View(title = "半年内是否有采购需求", show = false),
            edit = @Edit(title = "半年内是否有采购需求", type = EditType.CHOICE, readonly = @Readonly(add = false, edit = false, exprHandler = LineDownLimitEdit.class), search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "是", value = "1"),
                            @VL(label = "否", value = "2"),
                            @VL(label = "待确定", value = "3"),
                    }
            )))
    @Comment("半年内是否有采购需求")
    @ApiModelProperty("半年内是否有采购需求")
    private String halfYearPurchaseDemand;

    @EruptField(
            views = @View(title = "是否拜访", show = false),
            edit = @Edit(title = "是否拜访", type = EditType.CHOICE, readonly = @Readonly(add = false, edit = false, exprHandler = LineDownLimitEdit.class), search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "是", value = "1"),
                            @VL(label = "否", value = "2"),
                    }
            )))
    @Comment("是否拜访")
    @ApiModelProperty("是否拜访")
    private String isVisit;

    @EruptField(
            views = @View(title = "商机状态", show = false))
    @Comment("商机状态")
    @ApiModelProperty("商机状态")
    private String businessStatus;

    @EruptField(
            views = @View(title = "商机状态", show = true),
            edit = @Edit(title = "商机状态", type = EditType.CHOICE, readonly = @Readonly(), search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "未转单", value = "未转单"),
                            @VL(label = "跟进中", value = "跟进中"),
                            @VL(label = "结束", value = "结束"),
                    }
            )))
    @Comment("商机状态")
    @ApiModelProperty("商机状态")
    private String businessStatusShow;

    @EruptField(
            views = @View(title = "转单时间"),
            edit = @Edit(title = "转单时间", type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true), readonly = @Readonly()))
    @Comment("转单时间")
    @ApiModelProperty("转单时间")
    private String date;

    @EruptField(
            views = @View(title = "更新时间"),
            edit = @Edit(title = "更新时间", type = EditType.INPUT, readonly = @Readonly))
    @Comment("更新时间")
    @ApiModelProperty("更新时间")
    private String lineDownDate;


}

