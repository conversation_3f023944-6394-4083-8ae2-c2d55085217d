package com.ydy.dingtalk.businessFlow.handler;

import cn.hutool.core.date.DateUtil;
import com.ydy.dingtalk.businessFlow.entity.BusinessOrderFeedBack;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferUser;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessTransferUserSelect;
import com.ydy.dingtalk.businessFlow.util.DingTalkSendMsgUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.transaction.Transactional;
import java.util.List;


@Component
@Slf4j
public class BusinessToTransferUser implements OperationHandler<BusinessTransferOrder, BusinessTransferUser> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private DingTalkSendMsgUtil dingTalkSendMsgUtil;

    @Override
    @SneakyThrows
    @Transactional
    public String exec(List<BusinessTransferOrder> data, BusinessTransferUser businessTransferUser, String[] param) {
        BusinessTransferOrder businessTransferOrder = data.get(0);
        if(businessTransferOrder.getBusinessStatus().equals("4")){
            throw new EruptApiErrorTip("已结束，不可更改面销人!");
        }
        if(businessTransferOrder.getBusinessStatus().equals("1")){
            throw new EruptApiErrorTip("尚未提交，不可更改面销人!");
        }
        String sql1 = "select id FROM e_upms_user WHERE name = (:names)";
        List name = eruptDao.getEntityManager().createNativeQuery(sql1)
                .setParameter("names", businessTransferUser.getBusinessOrderFeedback())
                .getResultList();
        String s = name.get(0).toString();

        String isInCompany = "select a.id from e_business_line_down_config a where a.user_status = '0' AND a.self_id = "+ s;
        List resultList1 = eruptDao.getEntityManager().createNativeQuery(isInCompany).getResultList();
        if (resultList1.size()==0){
            throw new EruptApiErrorTip("转单人不在公司内，请联系管理员!");
        }
        String yunName = businessTransferOrder.getEffectiveFollowNum();
        String jpqlToSelectYun = "SELECT id FROM e_business_line_down_tag WHERE name = (:name)";
        Query queryToSelectYun = eruptDao.getEntityManager().createNativeQuery(jpqlToSelectYun);
        queryToSelectYun.setParameter("name", yunName);
        List yunList = queryToSelectYun.getResultList();
        long yunId = Long.parseLong(yunList.get(0).toString());
        String isCanStatus = "select a.id from e_business_line_down_config a JOIN e_line_down_user_tag b ON a.id = b.table_id where a.self_id = "+ s +" AND b.tag_id = " + yunId;
        Query nativeQuery = eruptDao.getEntityManager().createNativeQuery(isCanStatus);
        List resultList = nativeQuery.getResultList();
        if(resultList.size() == 0){
            throw new EruptApiErrorTip("转单人所属云类型不符合商机云类型!");
        }
        String toInsert = "INSERT INTO e_business_transactions_record (receive_user,to_transfer_user,do_user,dates,business_id) VALUES (:name,:username,:localName,:dates,:businessId)";
        eruptDao.getEntityManager().createNativeQuery(toInsert)
                .setParameter("name", businessTransferUser.getBusinessOrderFeedback())
                .setParameter("username", businessTransferOrder.getLineDownUserName())
                .setParameter("localName", eruptUserService.getCurrentEruptUser().getName())
                .setParameter("dates", DateUtil.now())
                .setParameter("businessId",businessTransferOrder.getId().toString())
                .executeUpdate();
        businessTransferOrder.setLineDownUserName(businessTransferUser.getBusinessOrderFeedback());
        businessTransferOrder.setLineDownUser(s);
        businessTransferOrder.setLineDownDate(DateUtil.now());

        // 发送钉钉消息
        try {
            dingTalkSendMsgUtil.businessFlowConstSendMsgToLineDown(businessTransferOrder.getLineDownUserName(), businessTransferOrder.getCompanyName());
        }catch (Exception e) {
            log.error("缺少token或未找到面销人，给接单人发送钉钉消息通知失败！666");
        }
        return null;
    }
}
