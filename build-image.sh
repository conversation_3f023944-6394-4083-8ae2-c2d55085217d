#!/bin/bash
PROJECT_NAME=ydy-dingtalk
PROJECT_DESC=钉钉平台服务

echo '当前项目名称为:' "$PROJECT_NAME" - "$PROJECT_DESC"

# 从命令行参数中获取密码
password=$1

# 检查是否提供了密码
if [ -z "$password" ]; then
  echo "Error: 请提供Docker密码作为第一个参数。"
  exit 1
fi

version=$(grep --max-count=1 '<version>' pom.xml | awk -F '>' '{ print $2 }' | awk -F '<' '{ print $1 }')-$(date '+%Y%m%d%H%M%S')
echo "待构建的 $PROJECT_DESC 镜像版本为: $version"

chmod 777 -R .
cp -f Dockerfile ../target/

echo "正在构建 $PROJECT_DESC 镜像..."
registry=registry.cn-wulanchabu.aliyuncs.com
imagePro=registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/$PROJECT_NAME

cd ../target
echo '当前工作目录为:' "$PWD"
docker build -t $imagePro:$version -t $imagePro .
docker login --username=candycloud $registry --password=$password
docker push $imagePro:$version

echo "正在清理 $PROJECT_DESC 构建环境..."
docker rmi $imagePro:$version
docker rmi $imagePro:latest
find . -type d -name "target" -exec rm -rf {} +

echo "$PROJECT_DESC 构建完成！"

echo "正在更新 $PROJECT_DESC 镜像..."
kubectl set image deploy/$PROJECT_NAME $PROJECT_NAME=$imagePro:$version
