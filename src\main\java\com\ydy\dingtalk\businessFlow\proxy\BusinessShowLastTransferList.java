package com.ydy.dingtalk.businessFlow.proxy;

import org.springframework.stereotype.Component;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Set;

@Component
public class BusinessShowLastTransferList implements ExprBool.ExprHandler  {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public boolean handler(boolean expr, String[] params) {
        if(eruptUserService.getCurrentEruptUser().getId() == 1){
            return true;
        }
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        for(EruptRole role : roles){
            if (role.getName().equals("商机流转管理-主管") || role.getName().equals("商机流转管理-管理员")){
                return true;
            }
        }
        return false;
    }
}
