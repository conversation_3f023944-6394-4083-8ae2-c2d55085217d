package com.ydy.dingtalk.businessFlow.entity;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.jpa.model.MetaModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "e_business_line_down_tag")
@Erupt(
        name = "标签表"
)
public class YunTypeTag extends MetaModel {
    @EruptField(
            views = @View(title = "云名称"),
            edit = @Edit(title = "云名称")
    )
    private String name;

    @EruptField(
            views = @View(title = "云备注"),
            edit = @Edit(title = "云备注")
    )
    private String remark;
}
