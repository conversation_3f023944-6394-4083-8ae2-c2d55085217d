variables:
  GIT_CEILING_DIRECTORIES: /home/<USER>/builds/
  GIT_STRATEGY: "clone"
  REMOTE_HOST: "*************"
  REMOTE_USER: "root"

.template: &template
  tags:
    - erupt

stages:
  - build
  - notify

build-job:
  <<: *template
  variables:
    GIT_STRATEGY: "fetch"
  stage: build
  only:
    - /^master/
  script:
    - echo "开始构建...."
    - chown -R $USER:$USER .  # 将所有文件的所有权更改为当前用户
    - chmod -R u+w .          # 确保所有文件对当前用户有写权限
    - echo "设置SSH私钥..."
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY_LOCAL30" | tr -d '\r' > ~/.ssh/id_rsa  # 使用 GitLab CI/CD 变量中的私钥内容
    - chmod 600 ~/.ssh/id_rsa  # 确保私钥文件权限正确
    - ssh-keyscan -H $REMOTE_HOST >> ~/.ssh/known_hosts  # 自动添加主机密钥到 known_hosts
    - echo '当前工作目录:' "$PWD"
    - echo "连接Runner服务器..."
    - ssh $REMOTE_USER@$REMOTE_HOST "cd $PWD && sh build-java.sh && sh build-image.sh $DOCKER_REGISTRY_PWD"
