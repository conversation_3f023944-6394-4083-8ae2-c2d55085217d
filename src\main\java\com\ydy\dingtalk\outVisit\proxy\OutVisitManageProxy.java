package com.ydy.dingtalk.outVisit.proxy;

import com.ydy.dingtalk.outVisit.entity.OutVisitManage;
import com.ydy.dingtalk.wxfriends.handler.AdminSearch;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.QueryExpression;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since :2023/10/11:9:29
 */
@Service
public class OutVisitManageProxy implements DataProxy<OutVisitManage> {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(OutVisitManage outVisitManage) {
       outVisitManage.setVisitUser(eruptUserService.getCurrentEruptUser().getId());
       outVisitManage.setVisitUserName(eruptUserService.getCurrentEruptUser().getName());
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Map<String, String> accountMap = AdminSearch.getIAdminOut();
        if(!(currentEruptUser!=null&&accountMap.containsKey(currentEruptUser.getAccount()))){
            conditions.add(new Condition("visitUser",currentEruptUser.getId(), QueryExpression.EQ));

        }
        return null;
    }

    @Override
    public void beforeAdd(OutVisitManage outVisitManage) {
        // 查询今日是否创建
        OutVisitManage outVisitManage1 = EruptDaoUtils.selectOne("SELECT * FROM tb_out_manage where CONVERT('"+outVisitManage.getDate()+"',DATE) = CONVERT(date,DATE) and create_by ='" + outVisitManage.getVisitUserName()+"'", OutVisitManage.class);
        if (ObjectUtils.isNotEmpty(outVisitManage1)) {
            NotifyUtils.showErrorDialog("每天只可填写一次!");
        }
        //设置创建时间
        outVisitManage.setCreateTime(LocalDateTime.now());
        //设置创建人
        outVisitManage.setCreateBy(eruptUserService.getCurrentEruptUser().getName());


    }


    @Override
    public void beforeUpdate(OutVisitManage outVisitManage) {

        OutVisitManage outVisitManage1 = EruptDaoUtils.selectOne("SELECT * FROM tb_out_manage where CONVERT('"+outVisitManage.getDate()+"',DATE) = CONVERT(date,DATE) and create_by ='" + outVisitManage.getVisitUserName()+"'", OutVisitManage.class);
        if (ObjectUtils.isNotEmpty(outVisitManage1)) {
            if (outVisitManage.getId() != outVisitManage1.getId()) {
                NotifyUtils.showErrorDialog("有重复日期，请检查日期!");
            }
        }

    }
}
