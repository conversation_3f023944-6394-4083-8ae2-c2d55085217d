package com.ydy.dingtalk.oa.entity;

import lombok.Data;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BatchProcessResult {
    private int totalCount = 0;           // 总处理数量
    private int successCount = 0;         // 成功数量
    private List<String> skippedItems = new ArrayList<>();    // 跳过的项目（已存在）
    private Map<String, String> failedItems = new HashMap<>(); // 失败的项目及原因
    
    public void incrementTotal() {
        totalCount++;
    }
    
    public void incrementSuccess() {
        successCount++;
    }
    
    public void addSkipped(String subject) {
        skippedItems.add(subject);
    }
    
    public void addFailed(String subject, String reason) {
        failedItems.put(subject, reason);
    }
    
    public String getSummary() {
        return String.format("总数: %d, 成功: %d, 跳过: %d, 失败: %d", 
            totalCount, successCount, skippedItems.size(), failedItems.size());
    }
} 