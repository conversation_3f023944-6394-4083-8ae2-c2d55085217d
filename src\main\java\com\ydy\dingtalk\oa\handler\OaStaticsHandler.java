package com.ydy.dingtalk.oa.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.bi.fun.EruptBiHandler;

import java.util.Map;

@Service
public class OaStaticsHandler implements EruptBiHandler {

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        StringBuilder sb = new StringBuilder();
        condition.forEach((key, value) -> {
            if (value != null && !key.contains("__")) {
                switch (key) {
                    case "employee":
                        if (value instanceof java.util.List && !((java.util.List<?>) value).isEmpty()) {
                            java.util.List<?> employees = (java.util.List<?>) value;
                            sb.append(" AND employee_name IN (");
                            for (int i = 0; i < employees.size(); i++) {
                                if (i > 0) {
                                    sb.append(",");
                                }
                                sb.append("'").append(employees.get(i)).append("'");
                            }
                            sb.append(")");
                        }
                        break;
                    case "date":
                        if (value instanceof java.util.List && ((java.util.List<?>) value).size() == 2) {
                            java.util.List<?> dateRange = (java.util.List<?>) value;
                            String startDateFull = dateRange.get(0).toString();
                            String endDateFull = dateRange.get(1).toString();
                            
                            // Extract only the year-month-day portion (YYYY-MM-DD)
                            String startDate = startDateFull.substring(0, 10);
                            String endDate = endDateFull.substring(0, 10);
                            
                            sb.append(" AND date BETWEEN '").append(startDate).append("' AND '").append(endDate).append("'");
                        }
                        break;
                }
            }
        });
        String conditionSql = sb.toString();
        return expr.replace("#REPLACE", conditionSql);
    }
}
