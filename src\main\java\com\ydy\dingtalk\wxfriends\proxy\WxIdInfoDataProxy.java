package com.ydy.dingtalk.wxfriends.proxy;

import com.ydy.dingtalk.wxfriends.entity.WxIdInfo;
import com.ydy.dingtalk.wxfriends.handler.AdminSearch;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.QueryExpression;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class WxIdInfoDataProxy implements DataProxy<WxIdInfo> {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(WxIdInfo wxIdInfo) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        if(currentEruptUser.getIsAdmin()){
            return;
        }
        //获取当前组织
        EruptOrg eruptOrg = currentEruptUser.getEruptOrg();
        //设置团队
        wxIdInfo.setSubDeptId(String.valueOf(eruptOrg.getId()));
        //设置使用人
        wxIdInfo.setCurrUser(currentEruptUser.getName());
    }

    @Override
    public void beforeAdd(WxIdInfo wxIdInfo) {
        //设置创建时间
        wxIdInfo.setCreateTime(LocalDateTime.now());
        //设置创建人
        wxIdInfo.setCreateBy(eruptUserService.getCurrentEruptUser().getName());
        //获取表名
//        String table = EruptUtil.getTable(WxIdInfo.class);
//        String phoneSql = String.format("select * from `%s` where mobile='%s'", table, wxIdInfo.getMobile());
//        WxIdInfo existWxIdInfo = EruptDaoUtils.selectOne(phoneSql, WxIdInfo.class);
//        if (existWxIdInfo != null && !existWxIdInfo.getCurrUser().equals(wxIdInfo.getCurrUser())) {
//            NotifyUtils.showErrorDialog("当前手机号已被使用，请换一个！");
//        }
    }

    @Override
    public void beforeUpdate(WxIdInfo wxIdInfo) {
        //设置修改时间
        wxIdInfo.setUpdateTime(LocalDateTime.now());
        //设置修改人
        wxIdInfo.setUpdateBy(eruptUserService.getCurrentEruptUser().getName());
        //获取表名
//        String table = EruptUtil.getTable(WxIdInfo.class);
//        String phoneSql = String.format("select * from `%s` where mobile='%s'", table, wxIdInfo.getMobile());
//        WxIdInfo existWxIdInfo = EruptDaoUtils.selectOne(phoneSql, WxIdInfo.class);
//        if (existWxIdInfo != null && !existWxIdInfo.getId().equals(wxIdInfo.getId()) && !existWxIdInfo.getCurrUser().equals(wxIdInfo.getCurrUser())) {
//            NotifyUtils.showErrorDialog("当前手机号已被使用，请换一个！");
//        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Map<String, String> accountMap = AdminSearch.getAdmin();
        if(!(currentEruptUser!=null&&accountMap.containsKey(currentEruptUser.getAccount()))){
            conditions.add(new Condition("currUser",currentEruptUser.getName(),QueryExpression.EQ));

        }
        return null;
    }
}
