/*
 Navicat Premium Data Transfer

 Source Server         : 微信好友统计数据库
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : dev-aws.yundingyun.net:30036
 Source Schema         : ydy-dingtalk

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 26/02/2024 10:35:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for e_bi
-- ----------------------------
DROP TABLE IF EXISTS `e_bi`;
CREATE TABLE `e_bi`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `cache_time` int NULL DEFAULT NULL COMMENT '缓存时间（秒）',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `count_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '总条数SQL',
  `export` bit(1) NULL DEFAULT NULL COMMENT '导出',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `page_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分页方式',
  `refresh_time` int NULL DEFAULT NULL COMMENT '自动刷新周期（秒）',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表描述',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '取值SQL',
  `bi_group_id` bigint NULL DEFAULT NULL COMMENT '组别',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `datasource_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  `top_tips` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表顶部提示',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKls8yyipfi4uh5dp6kkvnffr9s`(`code` ASC) USING BTREE,
  INDEX `FKkdvrjqg0be7n88kr8circjt40`(`bi_group_id` ASC) USING BTREE,
  INDEX `FKpmu0yvj13ahrp2s7nfn2nr1q3`(`class_handler_id` ASC) USING BTREE,
  INDEX `FKmmt5bs9246wwljcur1ccnkdg7`(`datasource_id` ASC) USING BTREE,
  CONSTRAINT `FKkdvrjqg0be7n88kr8circjt40` FOREIGN KEY (`bi_group_id`) REFERENCES `e_bi_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKmmt5bs9246wwljcur1ccnkdg7` FOREIGN KEY (`datasource_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpmu0yvj13ahrp2s7nfn2nr1q3` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报表配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi
-- ----------------------------
INSERT INTO `e_bi` VALUES (1, '系统', '2023-09-26 10:10:43', '系统管理员', '2023-10-15 19:41:41', 1, 'WxFriendsStat', NULL, b'1', '微信新增统计', 'backend', 60, NULL, NULL, 2, NULL, 1, NULL);
INSERT INTO `e_bi` VALUES (2, '系统管理员', '2024-01-15 15:03:22', '系统管理员', '2024-01-19 15:30:20', 1, 'WxOutvisitStat', NULL, b'1', '每日跟进统计', 'backend', 60, NULL, NULL, 4, NULL, 1, NULL);

-- ----------------------------
-- Table structure for e_bi_chart
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_chart`;
CREATE TABLE `e_bi_chart`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `cache_time` int NULL DEFAULT NULL COMMENT '缓存时间（秒）',
  `chart_option` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义图表配置',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `grid` int NULL DEFAULT NULL COMMENT '栅格数',
  `height` int NULL DEFAULT NULL COMMENT '高度(px)',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `sort` int NULL DEFAULT NULL COMMENT '显示顺序',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图表SQL',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图表类型',
  `bi_id` bigint NULL DEFAULT NULL,
  `bi_tpl_id` bigint NULL DEFAULT NULL COMMENT '报表模板',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `data_source_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKmcmnjuj7y5ppitao1jck5s2xy`(`code` ASC, `bi_id` ASC) USING BTREE,
  INDEX `FK6012onu7tuk4yx1734cqa39uk`(`bi_id` ASC) USING BTREE,
  INDEX `FK4jc135g48190nvujmk88q10mw`(`bi_tpl_id` ASC) USING BTREE,
  INDEX `FKqqhuuqs4rnvt8rwjcqmiei6gq`(`class_handler_id` ASC) USING BTREE,
  INDEX `FK7kyuns0awcjn0ywf0ma2gfm3d`(`data_source_id` ASC) USING BTREE,
  CONSTRAINT `FK4jc135g48190nvujmk88q10mw` FOREIGN KEY (`bi_tpl_id`) REFERENCES `e_bi_tpl` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK6012onu7tuk4yx1734cqa39uk` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK7kyuns0awcjn0ywf0ma2gfm3d` FOREIGN KEY (`data_source_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqqhuuqs4rnvt8rwjcqmiei6gq` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '图表配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_chart
-- ----------------------------
INSERT INTO `e_bi_chart` VALUES (1, '系统', '2023-09-26 10:28:14', '系统管理员', '2024-01-02 13:42:08', 1, NULL, 'aquQwvkW', 24, 240, '微信好友统计列表', 1, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t2.curr_user \'使用人\',\r\n	t2.wx_id \'微信号\',\r\n	t2.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t2.phone \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_add t2 INNER JOIN e_upms_org t3 ON t2.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC,t2.curr_user ASC;', 'table', 1, NULL, 2, 1);
INSERT INTO `e_bi_chart` VALUES (2, '系统', '2023-09-26 10:28:50', '系统', '2023-10-10 16:07:16', 1, NULL, 'dvolYbqR', 24, 340, '新增好友统计柱状图', 2, 'SELECT\r\n	`curr_user` name,\r\n	SUM( `curr_count` ) curr_count,\r\n	\'当前总量\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name \r\nUNION ALL\r\nSELECT\r\n	`curr_user` name,\r\n	SUM( `this_week_count` ) curr_count,\r\n	\'本周新增\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name;\r\n', 'Column', 1, NULL, 1, 1);
INSERT INTO `e_bi_chart` VALUES (3, '系统管理员', '2024-01-15 16:36:39', '系统管理员', '2024-01-22 14:18:36', 1, NULL, 'hxsQfTeC', 24, 240, '统计列表', 1, 'SELECT visit_user_name \'拜访人\', \r\n       SUM(add_wx_friend) AS  \'添加微信好友数\', \r\n       SUM(add_business_opportunities) AS  \'新增商机数\', \r\n       SUM(t1.effective_business_amount) AS  \'今日有效业务金额\', \r\n       SUM(effective_follow_num) AS  \'有效跟进记录数\', \r\n       SUM(follow_num) AS  \'跟进记录总数\', \r\n       SUM(visit_customers_num) AS  \'外出拜访客户数\'\r\n       \r\nFROM tb_out_manage t1\r\n\r\n#REPLACE\r\n\r\nGROUP BY visit_user_name', 'table', 2, NULL, 3, 1);
INSERT INTO `e_bi_chart` VALUES (4, '系统管理员', '2024-01-15 18:00:59', '系统管理员', '2024-01-22 14:38:46', 1, '{\r\n    \"legend\": {\r\n        \"position\": \"bottom\",\r\n        \"visible\":true\r\n    }\r\n}', 'dnvFkMiO', 24, 340, '折线图', 2, 'SELECT\r\n    \r\n    `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name ,shijian\r\n', 'Line', 2, NULL, 4, 1);

-- ----------------------------
-- Table structure for e_bi_class_handler
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_class_handler`;
CREATE TABLE `e_bi_class_handler`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `handler_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理类',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理类参数',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报表处理类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_class_handler
-- ----------------------------
INSERT INTO `e_bi_class_handler` VALUES (1, '系统', '2023-09-26 10:01:08', '系统', '2023-09-26 10:00:49', 'com.ydy.dingtalk.wxfriends.handler.BiHandler', '微信好友统计报表处理类', NULL, NULL);
INSERT INTO `e_bi_class_handler` VALUES (2, '系统', '2023-09-26 10:01:25', '系统', '2023-09-26 10:00:49', 'com.ydy.dingtalk.wxfriends.handler.BiHandlerList', '微信好友统计列表处理类', NULL, NULL);
INSERT INTO `e_bi_class_handler` VALUES (3, '系统管理员', '2024-01-15 14:39:31', '系统管理员', '2024-01-15 14:39:10', 'com.ydy.dingtalk.outVisit.handler.OutVisitBihandler', '每日客户跟进报表', NULL, NULL);
INSERT INTO `e_bi_class_handler` VALUES (4, '系统管理员', '2024-01-15 14:39:49', '系统管理员', '2024-01-15 14:39:37', 'com.ydy.dingtalk.outVisit.handler.OutVisitBihandlerList', '每日统计图表', NULL, NULL);

-- ----------------------------
-- Table structure for e_bi_column
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_column`;
CREATE TABLE `e_bi_column`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `display` bit(1) NULL DEFAULT NULL COMMENT '是否显示',
  `drill_express` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '下钻SQL',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列名',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `sortable` bit(1) NULL DEFAULT NULL COMMENT '是否排序',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型',
  `width` int NULL DEFAULT NULL COMMENT '宽度',
  `bi_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK7ofdtmq1g18px49itii3ifita`(`bi_id` ASC) USING BTREE,
  CONSTRAINT `FK7ofdtmq1g18px49itii3ifita` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '列配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_column
-- ----------------------------

-- ----------------------------
-- Table structure for e_bi_datasource
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_datasource`;
CREATE TABLE `e_bi_datasource`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `driver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '驱动',
  `limit_sql` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分页语句',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `pool_config` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '连接池配置',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库类型',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '连接字符串',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKnl4a4ys6lomm3sxjv5nqc2aar`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据源管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_datasource
-- ----------------------------
INSERT INTO `e_bi_datasource` VALUES (1, '系统', '2023-09-26 10:00:09', '系统', '2023-09-26 09:56:36', 'eA9XcQhG', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '微信好友统计', '1234@abcD', NULL, '微信好友统计 数据源管理', 'MySQL', '****************************************************************************************************************************************************************************************************************************************************', 'root');
INSERT INTO `e_bi_datasource` VALUES (2, '系统管理员', '2024-01-15 14:24:48', '系统管理员', '2024-01-15 14:24:25', 'PUIRmdyk', 'com.p6spy.engine.spy.P6SpyDriver', NULL, '每日客户跟进', '1234@abcD', NULL, '每日客户跟进', 'MySQL', '****************************************************************************************************************************************************************************************************************************************************', 'root');

-- ----------------------------
-- Table structure for e_bi_dimension
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_dimension`;
CREATE TABLE `e_bi_dimension`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度编码',
  `default_value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认值',
  `not_null` bit(1) NULL DEFAULT NULL COMMENT '是否必填',
  `sort` int NULL DEFAULT NULL COMMENT '显示顺序',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度类型',
  `bi_id` bigint NULL DEFAULT NULL,
  `bi_dimension_reference_id` bigint NULL DEFAULT NULL COMMENT '参照维度',
  `is_show` bit(1) NULL DEFAULT NULL COMMENT '是否显示',
  `col` int NULL DEFAULT NULL COMMENT '宽度占比',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK178xcwr6hc4s2q6radi3drlun`(`code` ASC, `bi_id` ASC) USING BTREE,
  INDEX `FKtak80kcehioa5lt77llqh8c2a`(`bi_id` ASC) USING BTREE,
  INDEX `FKwpl79gqh3v35vr87ihk842lx`(`bi_dimension_reference_id` ASC) USING BTREE,
  CONSTRAINT `FKtak80kcehioa5lt77llqh8c2a` FOREIGN KEY (`bi_id`) REFERENCES `e_bi` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKwpl79gqh3v35vr87ihk842lx` FOREIGN KEY (`bi_dimension_reference_id`) REFERENCES `e_bi_dimension_reference` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '查询维度' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_dimension
-- ----------------------------
INSERT INTO `e_bi_dimension` VALUES (1, 'weekSearch', NULL, b'0', 1, '周数筛选', 'REFERENCE', 1, 4, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (2, 'peopleSearch', NULL, b'0', 2, '人员筛选', 'REFERENCE', 1, 1, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (3, 'monthSearch', NULL, b'0', 3, '月份筛选', 'MONTH', 1, NULL, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (4, 'teamSearch', NULL, b'0', 4, '团队筛选', 'REFERENCE', 1, 2, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (5, 'mobileSearch', NULL, b'0', 5, '手机号查询', 'REFERENCE', 1, 3, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (6, 'dateSearch', NULL, b'0', 6, '日期筛选', 'DATE', 1, NULL, NULL, NULL);
INSERT INTO `e_bi_dimension` VALUES (7, 'visitUserNameSearch', NULL, b'0', 1, '提交人查询', 'REFERENCE', 2, 5, b'1', NULL);
INSERT INTO `e_bi_dimension` VALUES (8, 'outDateSearch', NULL, b'0', 2, '日期筛选', 'DATE_RANGE', 2, NULL, b'1', NULL);

-- ----------------------------
-- Table structure for e_bi_dimension_reference
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_dimension_reference`;
CREATE TABLE `e_bi_dimension_reference`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `ref_sql` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参照SQL',
  `class_handler_id` bigint NULL DEFAULT NULL COMMENT '处理类',
  `data_source_id` bigint NULL DEFAULT NULL COMMENT '数据源',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKo1gd6tkp2vcujvfx74n0uj63g`(`class_handler_id` ASC) USING BTREE,
  INDEX `FKghw1afiw0ms1p2arc49syayg3`(`data_source_id` ASC) USING BTREE,
  CONSTRAINT `FKghw1afiw0ms1p2arc49syayg3` FOREIGN KEY (`data_source_id`) REFERENCES `e_bi_datasource` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKo1gd6tkp2vcujvfx74n0uj63g` FOREIGN KEY (`class_handler_id`) REFERENCES `e_bi_class_handler` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参照维度' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_dimension_reference
-- ----------------------------
INSERT INTO `e_bi_dimension_reference` VALUES (1, '系统', '2023-09-26 10:02:36', '系统', '2023-09-26 10:01:51', '人员查询', 'SELECT DISTINCT curr_user from tb_wx_add;', NULL, 1);
INSERT INTO `e_bi_dimension_reference` VALUES (2, '系统', '2023-09-26 10:03:22', '系统管理员', '2023-11-14 17:37:41', '团队查询', 'SELECT `id`,`name` FROM e_upms_org WHERE\r\nparent_org_id IN (\r\n	SELECT `id` FROM e_upms_org WHERE\r\n		`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = \'营销中心\' ) AND `name` NOT IN (\'销售运营管理\',\'政企大客户部\',\'销售SaaS部\')\r\n	) OR `id` IN (\r\n	SELECT `id` FROM e_upms_org WHERE\r\n		`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = \'营销中心\' ) AND `name` NOT IN (\'销售运营管理\',\'政企大客户部\',\'销售SaaS部\')\r\n	)', NULL, 1);
INSERT INTO `e_bi_dimension_reference` VALUES (3, '系统', '2023-09-26 10:03:48', '系统', '2023-09-26 10:01:51', '手机号查询', 'SELECT DISTINCT mobile from tb_wx_id;', NULL, 1);
INSERT INTO `e_bi_dimension_reference` VALUES (4, '系统', '2023-09-26 10:04:12', '系统', '2023-09-26 10:01:51', '周数筛选', 'with recursive c(n) AS\r\n (\r\n select 1 \r\n union ALL\r\n select n + 1\r\n from c\r\n where n < 52\r\n )\r\n select * from c;', NULL, 1);
INSERT INTO `e_bi_dimension_reference` VALUES (5, '系统管理员', '2024-01-15 14:54:27', '系统管理员', '2024-01-15 14:54:09', '人员查询', 'SELECT visit_user_name from tb_out_manage ', NULL, 2);

-- ----------------------------
-- Table structure for e_bi_function
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_function`;
CREATE TABLE `e_bi_function`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `js_function` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '函数表达式',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKl4tkhdjrgmmkubp454rkxd2gs`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '函数管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_function
-- ----------------------------
INSERT INTO `e_bi_function` VALUES (1, NULL, NULL, NULL, NULL, 'BI_FUN', '', 'BI_FUN');

-- ----------------------------
-- Table structure for e_bi_group
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_group`;
CREATE TABLE `e_bi_group`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组别名称',
  `remark` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '上级组别',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKmgb9fx4s6cv7ixhtxh7yg4x3y`(`code` ASC) USING BTREE,
  INDEX `FK1p2nlcuafd5xb4bu4he0ft7p`(`parent_id` ASC) USING BTREE,
  CONSTRAINT `FK1p2nlcuafd5xb4bu4he0ft7p` FOREIGN KEY (`parent_id`) REFERENCES `e_bi_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分组管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_group
-- ----------------------------
INSERT INTO `e_bi_group` VALUES (1, NULL, NULL, NULL, NULL, 'default', '默认分组', NULL, NULL);
INSERT INTO `e_bi_group` VALUES (2, '系统', '2023-09-26 10:04:53', '系统', '2023-09-26 10:04:22', 'wxStaticial', '微信好友统计', '微信好友统计', NULL);
INSERT INTO `e_bi_group` VALUES (4, '系统管理员', '2024-01-15 14:56:40', '系统管理员', '2024-01-15 14:56:21', 'outVisitStaticial', '每日跟进统计', NULL, NULL);

-- ----------------------------
-- Table structure for e_bi_history
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_history`;
CREATE TABLE `e_bi_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `after_sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修改后表达式',
  `bi_id` bigint NULL DEFAULT NULL,
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '来源',
  `operate_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人',
  `operate_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `sql_statement` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修改前表达式',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDXfhl3a0gyqqa21ejanx90hmuyg`(`bi_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '修改记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_history
-- ----------------------------
INSERT INTO `e_bi_history` VALUES (1, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\' \r\nFROM\r\n	tb_wx_id t1\r\n	RIGHT OUTER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile\r\n	INNER JOIN e_upms_org t3 ON t1.sub_dept_id = t3.id \r\nORDER BY\r\n	t2.`date` DESC;', 1, '微信好友统计列表', 'erupt', '2023-10-09 10:05:18', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (2, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;', 1, '微信好友统计列表', 'erupt', '2023-10-09 10:06:51', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\' \r\nFROM\r\n	tb_wx_id t1\r\n	RIGHT OUTER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile\r\n	INNER JOIN e_upms_org t3 ON t1.sub_dept_id = t3.id \r\nORDER BY\r\n	t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (3, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	RIGHT OUTER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;', 1, '微信好友统计列表', 'erupt', '2023-10-09 10:07:59', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (4, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;', 1, '微信好友统计列表', 'erupt', '2023-10-09 10:34:46', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	RIGHT OUTER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (5, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t2.curr_user \'使用人\',\r\n	t2.wx_id \'微信号\',\r\n	t2.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t2.phone \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_add t2 INNER JOIN e_upms_org t3 ON t2.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;', 1, '微信好友统计列表', 'erupt', '2023-10-10 14:01:12', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t1.curr_user \'使用人\',\r\n	t1.wechat_id \'微信号\',\r\n	t1.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t1.mobile \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile INNER JOIN e_upms_org t3 ON t1.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (6, 'SELECT\r\n	t1.curr_user name,\r\n	sum( t2.curr_count ) curr,\r\n	\'当前总量\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.phone \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\nUNION ALL\r\nSELECT\r\n	t1.curr_user name,\r\n	sum( t2.this_week_count ) weekAdd,\r\n	\'本周新增\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.phone \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\n', 1, '新增好友统计柱状图', 'erupt', '2023-10-10 14:08:01', 'SELECT\r\n	t1.curr_user name,\r\n	sum( t2.curr_count ) curr,\r\n	\'当前总量\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\nUNION ALL\r\nSELECT\r\n	t1.curr_user name,\r\n	sum( t2.this_week_count ) weekAdd,\r\n	\'本周新增\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.wx_mobile \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\n');
INSERT INTO `e_bi_history` VALUES (7, 'SELECT\r\n	`curr_user` name,\r\n	SUM( `curr_count` ) curr_count,\r\n	\'当前总量\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name \r\nUNION ALL\r\nSELECT\r\n	`curr_user` name,\r\n	SUM( `this_week_count` ) curr_count,\r\n	\'本周新增\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name;\r\n', 1, '新增好友统计柱状图', 'erupt', '2023-10-10 16:07:16', 'SELECT\r\n	t1.curr_user name,\r\n	sum( t2.curr_count ) curr,\r\n	\'当前总量\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.phone \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\nUNION ALL\r\nSELECT\r\n	t1.curr_user name,\r\n	sum( t2.this_week_count ) weekAdd,\r\n	\'本周新增\' z\r\nFROM\r\n	tb_wx_id t1\r\n	INNER JOIN tb_wx_add t2 ON t1.mobile = t2.phone \r\n	#REPLACE\r\nGROUP BY\r\n	name\r\n');
INSERT INTO `e_bi_history` VALUES (8, 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t2.curr_user \'使用人\',\r\n	t2.wx_id \'微信号\',\r\n	t2.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t2.phone \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_add t2 INNER JOIN e_upms_org t3 ON t2.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC,t2.curr_user ASC;', 1, '微信好友统计列表', '系统管理员', '2024-01-02 13:42:08', 'SELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t2.curr_user \'使用人\',\r\n	t2.wx_id \'微信号\',\r\n	t2.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t2.phone \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_add t2 INNER JOIN e_upms_org t3 ON t2.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC;');
INSERT INTO `e_bi_history` VALUES (9, 'SELECT visit_user_name \'拜访人\', \r\n       SUM(add_wx_friend) AS  \'添加微信好友数\', \r\n       SUM(add_business_opportunities) AS  \'新增商机数\', \r\n       SUM(t1.effective_business_amount) AS  \'今日有效业务金额\', \r\n       SUM(effective_follow_num) AS  \'有效跟进记录数\', \r\n       SUM(follow_num) AS  \'跟进记录总数\', \r\n       SUM(visit_customers_num) AS  \'外出拜访客户数\'\r\n       \r\nFROM tb_out_manage t1\r\n\r\n#REPLACE\r\n\r\nGROUP BY visit_user_name', 2, '每日统计列表', '系统管理员', '2024-01-15 17:59:13', '\r\nSELECT\r\n	t2.`date` \'日期\',\r\n	t2.`week` \'周数\',\r\n	t3.`name` \'团队\',\r\n	t2.curr_user \'使用人\',\r\n	t2.wx_id \'微信号\',\r\n	t2.nickname \'昵称\',\r\n	t2.this_week_count \'本周新增\',\r\n	t2.phone \'手机号\',\r\n	t2.last_count \'上次总量\',\r\n	t2.curr_count \'当前总量\'\r\nFROM\r\n	tb_wx_add t2 INNER JOIN e_upms_org t3 ON t2.sub_dept_id=t3.id\r\n	#REPLACE\r\nORDER BY t2.`date` DESC,t2.curr_user ASC;');
INSERT INTO `e_bi_history` VALUES (10, 'SELECT\r\n	`visit_user_name` name,\r\n	SUM( `follow_num` ) curr_count,\r\n	\'跟进总量\' z \r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name \r\nUNION ALL\r\nSELECT\r\n	`visit_user_name` name,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	\'有效跟进记录\' z \r\nFROM\r\n	tb_out_manage t2\r\n	#REPLACE\r\nGROUP BY\r\n	name;', 2, '每日柱状图', '系统管理员', '2024-01-19 15:46:36', 'SELECT\r\n	`curr_user` name,\r\n	SUM( `curr_count` ) curr_count,\r\n	\'当前总量\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name \r\nUNION ALL\r\nSELECT\r\n	`curr_user` name,\r\n	SUM( `this_week_count` ) curr_count,\r\n	\'本周新增\' z \r\nFROM\r\n	tb_wx_add t2\r\n	#REPLACE\r\nGROUP BY\r\n	name;\r\n');
INSERT INTO `e_bi_history` VALUES (11, 'SELECT\r\n    \r\n    `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name ,shijian\r\nUNION ALL\r\nSELECT\r\n     `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage t2\r\n	#REPLACE\r\nGROUP BY\r\n	name,shijian', 2, '每日柱状图', '系统管理员', '2024-01-19 16:18:47', 'SELECT\r\n	`visit_user_name` name,\r\n	SUM( `follow_num` ) curr_count,\r\n	\'跟进总量\' z \r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name \r\nUNION ALL\r\nSELECT\r\n	`visit_user_name` name,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	\'有效跟进记录\' z \r\nFROM\r\n	tb_out_manage t2\r\n	#REPLACE\r\nGROUP BY\r\n	name;');
INSERT INTO `e_bi_history` VALUES (12, 'SELECT\r\n    \r\n    `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name ,shijian\r\n', 2, '折线图', '系统管理员', '2024-01-22 14:38:46', 'SELECT\r\n    \r\n    `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage  t2\r\n	#REPLACE\r\nGROUP BY\r\n	name ,shijian\r\nUNION ALL\r\nSELECT\r\n     `date` shijian,\r\n	SUM( `effective_follow_num` ) curr_count,\r\n	`visit_user_name` name\r\n	\r\nFROM\r\n	tb_out_manage t2\r\n	#REPLACE\r\nGROUP BY\r\n	name,shijian');

-- ----------------------------
-- Table structure for e_bi_tpl
-- ----------------------------
DROP TABLE IF EXISTS `e_bi_tpl`;
CREATE TABLE `e_bi_tpl`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '路径',
  `tpl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '模板',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKkqhrgyi70vv5d5mrv3h4sdwun`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '组件模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of e_bi_tpl
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
