package com.ydy.dingtalk.outVisit;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @since :2023/11/29:17:10
 */
public class Test {

    @SneakyThrows
    public static void main(String[] args) {
        OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential("dingzotjtfkfdrekd4yh", "NSWrh7nY9PIYQSX1z1abSCcSaFwIu8RWoZH4RwTUo7vAMxKdg9SHdAdU9qj2yO8g"))
                //注册事件监听
                .registerAllEventListener(new GenericEventListener() {
                    public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                        try {
                            //事件唯一Id
                            String eventId = event.getEventId();
                            //事件类型
                            String eventType = event.getEventType();
                            //事件产生时间
                            Long bornTime = event.getEventBornTime();
                            //获取事件体
                            System.out.println(event.getData());
                            JSONObject bizData = event.getData();
                            //处理事件
                          //  process(bizData);
                            //消费成功
                            return EventAckStatus.SUCCESS;
                        } catch (Exception e) {
                            //消费失败
                            return EventAckStatus.LATER;
                        }
                    }
                })
                .build().start();
    }

}
