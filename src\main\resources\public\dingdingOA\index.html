<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="./assets/uni.90cb8c2d.css">

    <meta charset="UTF-8" />
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.13.42/dingtalk.open.js"></script>
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
      window._AMapSecurityConfig = {
        securityJsCode: 'f3b5e575693f8d9ddbc578f3068a230a'
      }
      window.addEventListener('load', function() {
        if (window.dd) {
          dd.ready(function() {
            // 钉钉 JSAPI 已准备就绪
            window.__dd_ready = true;
          });
        }
      });
    </script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="./assets/index-DL5IdDYG.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/index-DTU0pRok.css">
  </head>
  <body>
    <div id="app"><!--app-html--></div>

  </body>
</html>
