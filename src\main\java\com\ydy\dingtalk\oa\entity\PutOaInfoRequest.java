package com.ydy.dingtalk.oa.entity;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "oa外出管理"
)
@Table(name = "tb_oa_info")
@Entity
@Getter
@Setter
@Comment("oa外出管理")
@ApiModel("oa外出管理")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class PutOaInfoRequest extends BaseModel {

    @EruptField(
            views = @View(title = "提交人"),
            edit = @Edit(title = "提交人", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("提交人")
    @ApiModelProperty("提交人")
    private String submitter;

    @EruptField(
            views = @View(title = "审批状态"),
            edit = @Edit(title = "审批状态", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("审批状态")
    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @EruptField(
            views = @View(title = "审批编号"),
            edit = @Edit(title = "审批编号", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("审批编号")
    @ApiModelProperty("审批编号")
    private String approvalNumber;

    @EruptField(
            views = @View(title = "所在部门"),
            edit = @Edit(title = "所在部门", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("所在部门")
    @ApiModelProperty("所在部门")
    private String department;

    @EruptField(
            views = @View(title = "出差事由"),
            edit = @Edit(title = "出差事由", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("出差事由")
    @ApiModelProperty("出差事由")
    private String tripReason;

    @EruptField(
            views = @View(title = "交通工具"),
            edit = @Edit(title = "交通工具", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("交通工具")
    @ApiModelProperty("交通工具")
    private String transport;

    @EruptField(
            views = @View(title = "单程往返"),
            edit = @Edit(title = "单程往返", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("单程往返")
    @ApiModelProperty("单程往返")
    private String roundTrip;

    @EruptField(
            views = @View(title = "出发地"),
            edit = @Edit(title = "出发地", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("出发地")
    @ApiModelProperty("出发地")
    private String departure;

    @EruptField(
            views = @View(title = "目的地"),
            edit = @Edit(title = "目的地", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("目的地")
    @ApiModelProperty("目的地")
    private String destination;

    @EruptField(
            views = @View(title = "开始时间"),
            edit = @Edit(title = "开始时间", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("开始时间")
    @ApiModelProperty("开始时间")
    private String startTime;

    @EruptField(
            views = @View(title = "结束时间"),
            edit = @Edit(title = "结束时间", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("结束时间")
    @ApiModelProperty("结束时间")
    private String endTime;

    @EruptField(
            views = @View(title = "时长"),
            edit = @Edit(title = "时长", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("时长")
    @ApiModelProperty("时长")
    private String duration;

    @EruptField(
            views = @View(title = "出差天数"),
            edit = @Edit(title = "出差天数", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("出差天数")
    @ApiModelProperty("出差天数")
    private String tripDays;

    @EruptField(
            views = @View(title = "同行人"),
            edit = @Edit(title = "同行人", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("同行人")
    @ApiModelProperty("同行人")
    private String companion;

    @EruptField(
            views = @View(title = "出行方式"),
            edit = @Edit(title = "出行方式", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("出行方式")
    @ApiModelProperty("出行方式")
    private String travelMode;

    @EruptField(
            views = @View(title = "公里数"),
            edit = @Edit(title = "公里数", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("公里数")
    @ApiModelProperty("公里数")
    private String distance;

    @EruptField(
            views = @View(title = "陪访人"),
            edit = @Edit(title = "陪访人", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("陪访人")
    @ApiModelProperty("陪访人")
    private String escort;

    @EruptField(
            views = @View(title = "公司名称"),
            edit = @Edit(title = "公司名称", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("公司名称")
    @ApiModelProperty("公司名称")
    private String companyName;

    @EruptField(
            views = @View(title = "公司地址"),
            edit = @Edit(title = "公司地址", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("公司地址")
    @ApiModelProperty("公司地址")
    private String companyAddress;

    @EruptField(
            views = @View(title = "客户名称"),
            edit = @Edit(title = "客户名称", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("客户名称")
    @ApiModelProperty("客户名称")
    private String clientName;

    @EruptField(
            views = @View(title = "客户职位"),
            edit = @Edit(title = "客户职位", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("客户职位")
    @ApiModelProperty("客户职位")
    private String clientPosition;

    @Type(type = "json")
    @Column(columnDefinition = "json", name = "multipleCompanies")
    @EruptField(
            views = @View(title = "多公司信息"),
            edit = @Edit(title = "多公司信息"))
    @Comment("多公司信息")
    @ApiModelProperty("多公司信息")
    private String multipleCompanies;

    @EruptField(
            views = @View(title = "oa类型"),
            edit = @Edit(title = "oa类型", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("oa类型")
    @ApiModelProperty("oa类型")
    private String oaType;

    @EruptField(
            views = @View(title = "外出类型"),
            edit = @Edit(title = "外出类型", type = EditType.INPUT, inputType = @InputType(), search = @Search(vague = true)))
    @Comment("外出类型")
    @ApiModelProperty("外出类型")
    private String outType;
}
