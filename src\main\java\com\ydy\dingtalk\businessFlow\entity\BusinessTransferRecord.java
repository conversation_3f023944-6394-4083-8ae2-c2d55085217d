package com.ydy.dingtalk.businessFlow.entity;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.MetaModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "e_business_transactions_record")
@Erupt(
        name = "转移记录表"
)
public class BusinessTransferRecord extends MetaModel {
    @EruptField(
            views = @View(title = "转单人")
    )
    private String toTransferUser;

    @EruptField(
            views = @View(title = "接单人")
    )
    private String receiveUser;

    @EruptField(
            views = @View(title = "操作人")
    )
    private String doUser;

    @EruptField(
            views = @View(title = "操作时间")
    )
    private String dates;

    @EruptField(
            views = @View(title = "商机转单Id",show = false)
    )
    private Long businessId;
}
