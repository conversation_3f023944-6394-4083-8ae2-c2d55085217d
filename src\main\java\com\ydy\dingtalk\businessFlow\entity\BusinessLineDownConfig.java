package com.ydy.dingtalk.businessFlow.entity;

import com.ydy.dingtalk.businessFlow.fileread.FileReadOnlyUtil;
import com.ydy.dingtalk.businessFlow.handler.BusinessToSubmit;
import com.ydy.dingtalk.businessFlow.handler.SyncDictButton;
import com.ydy.dingtalk.businessFlow.proxy.BusinessSubmitShow;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessLineDownNameUtil;
import com.ydy.dingtalk.businessFlow.proxy.BusinessLineDownProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;

import javax.persistence.*;
import java.util.Set;


@Erupt(
        name = "面销设置"
        ,rowOperation = {
                @RowOperation(
                        title = "同步云类型",
                        icon = "fa fa-user-circle-o",
                        operationHandler = SyncDictButton.class,
                        mode = RowOperation.Mode.BUTTON
                )
        },
                dataProxy = BusinessLineDownProxy.class
        )
@Table(name = "e_business_line_down_config")
@Entity
@Getter
@Setter
@Comment("面销设置")
@ApiModel("面销设置")
public class BusinessLineDownConfig  extends MetaModel {

    @EruptField(
            views = @View(title = "姓名",show = false),
            edit = @Edit(title = "姓名", type = EditType.INPUT,
                    inputType = @InputType(),show = false

            ))
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String selfId;

    @EruptField(views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", type = EditType.CHOICE,notNull = true,
                    readonly = @Readonly(add = false, edit = true),
                    choiceType = @ChoiceType(
                            fetchHandler = BusinessLineDownNameUtil.class,
                            fetchHandlerParams = {"α", "β", "γ"}//该值可被FetchHandlerImpl → fetch方法params参数获取到
                    )
            )
    )
    @Comment("姓名")
    @ApiModelProperty("姓名")
    private String selfName;

    @EruptField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,notNull = true,search = @Search(),choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "在司", value = "0"),
                            @VL(label = "外出", value = "1"),
                    }
            )))
    @Comment("状态")
    @ApiModelProperty("状态")
    private String userStatus;


    @ManyToMany  //多对多
    @JoinTable(
            name = "e_line_down_user_tag", //中间表表名，如下为中间表的定义，详见hibernate ManyToMany
            joinColumns = @JoinColumn(name = "table_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "tag_id", referencedColumnName = "id"))
    @EruptField(
            views = @View(title = "云类型"),
            edit = @Edit(
                    title = "云类型",
                    type = EditType.CHECKBOX,
                    notNull = true,
                    checkboxType = @CheckboxType
            )
    )
    @Comment("云类型")
    @ApiModelProperty("云类型")
    private Set<YunTypeTag> yunTags; //Tag对象定义如下👇
}
