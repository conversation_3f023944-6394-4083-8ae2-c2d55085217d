package com.ydy.dingtalk.businessFlow.entity;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import java.util.Date;

@Erupt(name = "e_business_order_feedback")
@Getter
@Setter
public class BusinessOrderFeedBack extends BaseModel {
    @EruptField(
            edit = @Edit(title = "成单反馈",type = EditType.CHOICE, notNull = true, search = @Search(), choiceType = @ChoiceType(
                    vl = {
                            @VL(label = "已成单", value = "1"),
                            @VL(label = "未成单", value = "2"),
                    }
            ))
    )
    private String businessOrderFeedback;
}
