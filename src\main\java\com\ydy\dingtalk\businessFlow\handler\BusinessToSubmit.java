package com.ydy.dingtalk.businessFlow.handler;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import com.ydy.dingtalk.businessFlow.util.DingTalkSendMsgUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.transaction.Transactional;
import java.util.*;

/**
 * <AUTHOR>
 * @since :2023/11/30:13:48
 */
@Slf4j
@Component
public class BusinessToSubmit implements OperationHandler<BusinessTransferOrder, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private DingTalkSendMsgUtil dingTalkSendMsgUtil;

    @Override
    @SneakyThrows
    @Transactional
    public String exec(List<BusinessTransferOrder> data, Void aVoid, String[] param) {
        BusinessTransferOrder businessTransferOrder = data.get(0);
        if(!businessTransferOrder.getBusinessStatus().equals("1")){
            throw new EruptApiErrorTip("已提交，不要重复提交！");
        }

        businessTransferOrder.setBusinessStatus("2");
        businessTransferOrder.setDate(DateUtil.now());
        businessTransferOrder.setBusinessStatusShow("跟进中");

        // 发送钉钉消息
        try {
            dingTalkSendMsgUtil.businessFlowConstSendMsgToLineDown(businessTransferOrder.getLineDownUserName(), businessTransferOrder.getCompanyName());
        }catch (Exception e) {
            log.error("缺少token或未找到面销人，给接单人发送钉钉消息通知失败！");
        }
        return null;
    }


}
