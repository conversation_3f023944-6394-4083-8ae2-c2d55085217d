package com.ydy.dingtalk.wxfriends.config;

import xyz.erupt.annotation.config.Comment;

public interface WxfriendConst {

    /**
     * 应用ID，与模块ID相同
     */
    String APPID = "wx-friends";

    String APPNAME = "微信好友统计";

    String AK = "dingzotjtfkfdrekd4yh";
    String SK = "NSWrh7nY9PIYQSX1z1abSCcSaFwIu8RWoZH4RwTUo7vAMxKdg9SHdAdU9qj2yO8g";

    @Comment("销售角色")
    String ROLE_SALES = "sales";
    @Comment("管理角色")
    String ROLE_MANAGER = "manager";

    @Comment("菜单入口")
    String MENU_ROOT = "$" + APPID;

    @Comment("统计菜单")
    String MENU_STAT = "/#/bi/WxFriendsStat";
}
