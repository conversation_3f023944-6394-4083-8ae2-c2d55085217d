package com.ydy.dingtalk.outVisit.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.bi.fun.EruptBiHandler;

import java.util.Map;

/**
 * <AUTHOR>
 * @since :2024/1/15:14:27
 */
@Service
public class OutVisitBihandler implements EruptBiHandler {

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        return OutCommonHandler.exprHandler(param,condition,expr);
    }
}
