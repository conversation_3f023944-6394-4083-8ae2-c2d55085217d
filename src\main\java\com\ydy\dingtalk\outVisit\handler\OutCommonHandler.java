package com.ydy.dingtalk.outVisit.handler;

import lombok.SneakyThrows;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */

public class OutCommonHandler {

    public static Map<String,String> initMap(){
        HashMap<String, String> result = new HashMap<>();
        result.put("visitUserNameSearch","visit_user_name");
        result.put("outDateSearch","date");
        return result;
    }


    public static String exprHandler(String param, Map<String, Object> condition, String expr){
        StringBuilder sb = new StringBuilder();
        Map<String, String> initMap = initMap();
        sb.append(" where 1=1 ");
        condition.forEach((key, value) -> {
            if (value != null && !key.contains("__")) {
                switch (key) {
                    case "visitUserNameSearch":
                        sb.append("and ").append(initMap.get(key)).append(" = ").append("'").append(value).append("'");
                        break;
                    default:

                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        Object value1 = value;
                        List<String> result = new ArrayList<String>();
                        if (value1 instanceof ArrayList<?>) {
                            for (Object o : (List<?>) value1) {
                                try {
                                    Date parse = simpleDateFormat.parse(o.toString());
                                    LocalDate localDate = parse.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                                    String localDateStr = localDate.format(fmt);
                                    result.add(localDateStr);
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                            }
                        }

                        sb.append("and ").append(initMap.get(key)).append(" BETWEEN ").append("'").append(result.get(0)).append("'").append(" and ").append("'").append(result.get(1)).append("'");
                        break;
                }
            }
        });
        String conditionSql = sb.toString();
        return expr.replace("#REPLACE", conditionSql);
    }
}
