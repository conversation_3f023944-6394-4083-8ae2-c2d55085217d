package com.ydy.dingtalk.businessFlow.handler;

import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;

@Component
public class BusinessAccept implements OperationHandler<BusinessTransferOrder, Void> {
    @Resource
    private EruptDao eruptDao;


    @Override
    @SneakyThrows
    @Transactional
    public String exec(List<BusinessTransferOrder> data, Void unused, String[] param) {
        BusinessTransferOrder businessTransferOrder = data.get(0);
        if(businessTransferOrder.getBusinessStatus().equals("4")){
            throw new EruptApiErrorTip("已结束，不可进行操作!");
        }
        if(businessTransferOrder.getBusinessStatus().equals("3")){
            throw new EruptApiErrorTip("已接单，不要重复接单！");
        }
        if(businessTransferOrder.getBusinessStatus().equals("1")){
            throw new EruptApiErrorTip("已拒单，不可再接单！");
        }
        businessTransferOrder.setBusinessStatus("3");
        return null;
    }
}
