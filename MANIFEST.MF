Manifest-Version: 1.0
Archiver-Version: Plexus Archiver
Created-By: Apache Maven
Built-By: sirius
Build-Jdk: 11.0.13
Main-Class: com.daliangang.Application
Class-Path: lib/spring-boot-starter-test-2.7.5.jar lib/spring-boot-sta
 rter-2.7.5.jar lib/spring-boot-2.7.5.jar lib/spring-context-5.3.23.ja
 r lib/spring-boot-autoconfigure-2.7.5.jar lib/spring-boot-starter-log
 ging-2.7.5.jar lib/logback-classic-1.2.11.jar lib/logback-core-1.2.11
 .jar lib/log4j-to-slf4j-2.17.2.jar lib/jul-to-slf4j-1.7.36.jar lib/ja
 karta.annotation-api-1.3.5.jar lib/snakeyaml-1.30.jar lib/spring-boot
 -test-2.7.5.jar lib/spring-boot-test-autoconfigure-2.7.5.jar lib/json
 -path-2.7.0.jar lib/json-smart-2.4.8.jar lib/accessors-smart-2.4.8.ja
 r lib/asm-9.1.jar lib/slf4j-api-1.7.36.jar lib/jakarta.xml.bind-api-2
 .3.3.jar lib/jakarta.activation-api-1.2.2.jar lib/assertj-core-3.22.0
 .jar lib/hamcrest-2.2.jar lib/junit-jupiter-5.8.2.jar lib/junit-jupit
 er-api-5.8.2.jar lib/opentest4j-1.2.0.jar lib/junit-platform-commons-
 1.8.2.jar lib/apiguardian-api-1.1.2.jar lib/junit-jupiter-params-5.8.
 2.jar lib/junit-jupiter-engine-5.8.2.jar lib/junit-platform-engine-1.
 8.2.jar lib/mockito-core-4.5.1.jar lib/byte-buddy-1.12.18.jar lib/byt
 e-buddy-agent-1.12.18.jar lib/objenesis-3.2.jar lib/mockito-junit-jup
 iter-4.5.1.jar lib/jsonassert-1.5.1.jar lib/android-json-0.0.20131108
 .vaadin1.jar lib/spring-core-5.3.23.jar lib/spring-jcl-5.3.23.jar lib
 /spring-test-5.3.23.jar lib/xmlunit-core-2.9.0.jar lib/erupt-upms-1.1
 1.2.6.jar lib/spring-boot-starter-data-redis-2.7.5.jar lib/spring-dat
 a-redis-2.7.5.jar lib/spring-data-keyvalue-2.7.5.jar lib/spring-data-
 commons-2.7.5.jar lib/spring-tx-5.3.23.jar lib/spring-oxm-5.3.23.jar
 lib/spring-aop-5.3.23.jar lib/spring-context-support-5.3.23.jar lib/l
 ettuce-core-6.1.10.RELEASE.jar lib/netty-common-4.1.84.Final.jar lib/
 netty-handler-4.1.84.Final.jar lib/netty-resolver-4.1.84.Final.jar li
 b/netty-buffer-4.1.84.Final.jar lib/netty-transport-native-unix-commo
 n-4.1.84.Final.jar lib/netty-codec-4.1.84.Final.jar lib/netty-transpo
 rt-4.1.84.Final.jar lib/reactor-core-3.4.24.jar lib/reactive-streams-
 1.0.4.jar lib/erupt-jpa-1.12.1.jar lib/spring-boot-starter-data-jpa
 -2.7.5.jar lib/spring-boot-starter-aop-2.7.5.jar lib/aspectjweaver-1.
 9.7.jar lib/jakarta.persistence-api-2.2.3.jar lib/spring-data-jpa-2.7
 .5.jar lib/spring-orm-5.3.23.jar lib/spring-aspects-5.3.23.jar lib/sp
 ring-boot-configuration-processor-2.7.5.jar lib/erupt-toolkit-1.12.1.
 6.jar lib/hutool-all-5.8.12.jar lib/pinyin4j-2.5.0.jar lib/apiAssert-
 2.0.0.jar lib/erupt-excel-1.12.1.jar lib/poi-5.2.3.jar lib/commons-
 collections4-4.4.jar lib/commons-math3-3.6.1.jar lib/SparseBitSet-1.2
 .jar lib/log4j-api-2.17.2.jar lib/poi-ooxml-5.2.3.jar lib/poi-ooxml-l
 ite-5.2.3.jar lib/xmlbeans-5.1.1.jar lib/commons-compress-1.21.jar li
 b/curvesapi-1.07.jar lib/jakarta.transaction-api-1.3.3.jar lib/UserAg
 entUtils-1.21.jar lib/ip2region-1.7.2.jar lib/easy-captcha-1.6.2.jar
 lib/erupt-security-1.12.1.jar lib/erupt-cloud-server-1.12.1.jar l
 ib/erupt-cloud-common-1.12.1.jar lib/erupt-common-1.12.1.jar lib/
 erupt-annotation-1.12.1.jar lib/commons-lang3-3.12.0.jar lib/common
 s-io-2.11.0.jar lib/gson-2.9.1.jar lib/jansi-2.4.0.jar lib/p6spy-3.9.
 1.jar lib/knife4j-openapi2-spring-boot-starter-4.0.0.jar lib/knife4j-
 core-4.0.0.jar lib/knife4j-openapi2-ui-4.0.0.jar lib/javassist-3.25.0
 -GA.jar lib/springfox-swagger2-2.10.5.jar lib/springfox-spi-2.10.5.ja
 r lib/springfox-core-2.10.5.jar lib/springfox-schema-2.10.5.jar lib/s
 pringfox-swagger-common-2.10.5.jar lib/springfox-spring-web-2.10.5.ja
 r lib/classgraph-4.1.7.jar lib/spring-plugin-core-2.0.0.RELEASE.jar l
 ib/spring-plugin-metadata-2.0.0.RELEASE.jar lib/mapstruct-1.3.1.Final
 .jar lib/swagger-models-1.6.6.jar lib/swagger-annotations-1.6.6.jar l
 ib/springfox-bean-validators-2.10.5.jar lib/springfox-spring-webmvc-2
 .10.5.jar lib/springfox-swagger-ui-2.10.5.jar lib/hutool-http-5.8.12.
 jar lib/hutool-core-5.8.12.jar lib/okhttp-4.9.3.jar lib/okio-2.8.0.ja
 r lib/kotlin-stdlib-common-1.6.21.jar lib/kotlin-stdlib-1.6.21.jar li
 b/annotations-13.0.jar lib/spring-cloud-starter-openfeign-3.1.3.jar l
 ib/spring-cloud-starter-3.1.3.jar lib/spring-cloud-context-3.1.3.jar
 lib/spring-security-rsa-1.0.10.RELEASE.jar lib/bcpkix-jdk15on-1.68.ja
 r lib/spring-cloud-openfeign-core-3.1.3.jar lib/feign-form-spring-3.8
 .0.jar lib/feign-form-3.8.0.jar lib/commons-fileupload-1.4.jar lib/sp
 ring-cloud-commons-3.1.3.jar lib/spring-security-crypto-5.7.4.jar lib
 /feign-core-11.8.jar lib/feign-slf4j-11.8.jar lib/feign-okhttp-8.9.2.
 jar lib/feign-core-8.9.2.jar lib/okhttp-2.5.0.jar lib/spring-cloud-st
 arter-loadbalancer-3.1.3.jar lib/spring-cloud-loadbalancer-3.1.3.jar
 lib/reactor-extra-3.4.8.jar lib/spring-boot-starter-cache-2.7.5.jar l
 ib/evictor-1.0.0.jar lib/erupt-tpl-1.12.1.jar lib/erupt-web-1.12.1.
 6.jar lib/spring-boot-starter-web-2.7.5.jar lib/spring-boot-starter-j
 son-2.7.5.jar lib/jackson-datatype-jdk8-2.13.4.jar lib/jackson-dataty
 pe-jsr310-2.13.4.jar lib/jackson-module-parameter-names-2.13.4.jar li
 b/spring-boot-starter-tomcat-2.7.5.jar lib/tomcat-embed-core-9.0.68.j
 ar lib/tomcat-embed-el-9.0.68.jar lib/tomcat-embed-websocket-9.0.68.j
 ar lib/spring-web-5.3.23.jar lib/spring-beans-5.3.23.jar lib/spring-w
 ebmvc-5.3.23.jar lib/spring-expression-5.3.23.jar lib/erupt-upload-co
 s-1.12.1.jar lib/erupt-core-1.12.1.jar lib/spring-boot-starter-jd
 bc-2.7.5.jar lib/HikariCP-4.0.3.jar lib/spring-jdbc-5.3.23.jar lib/hi
 bernate-core-5.6.12.Final.jar lib/jboss-logging-3.4.3.Final.jar lib/j
 avax.persistence-api-2.2.jar lib/antlr-2.7.7.jar lib/jboss-transactio
 n-api_1.2_spec-1.1.1.Final.jar lib/jandex-2.4.2.Final.jar lib/classma
 te-1.5.1.jar lib/javax.activation-api-1.2.0.jar lib/hibernate-commons
 -annotations-5.1.2.Final.jar lib/jaxb-api-2.3.1.jar lib/jaxb-runtime-
 2.3.7.jar lib/txw2-2.3.7.jar lib/istack-commons-runtime-3.0.12.jar li
 b/jakarta.activation-1.2.2.jar lib/cos_api-5.6.97.jar lib/httpclient-
 4.5.13.jar lib/httpcore-4.4.15.jar lib/commons-codec-1.15.jar lib/jod
 a-time-2.9.9.jar lib/jackson-databind-2.13.4.2.jar lib/jackson-annota
 tions-2.13.4.jar lib/jackson-core-2.13.4.jar lib/tencentcloud-sdk-jav
 a-kms-3.1.213.jar lib/tencentcloud-sdk-java-common-3.1.213.jar lib/co
 mmons-logging-1.2.jar lib/logging-interceptor-2.7.5.jar lib/bcprov-jd
 k15on-1.67.jar lib/erupt-dynamic-field-1.12.1.jar lib/erupt-devtool
 s-1.12.1.jar lib/erupt-devtools-common-1.12.1.jar lib/erupt-tpl-u
 i.avue-1.12.1.jar lib/smiley-http-proxy-servlet-1.12.1.jar lib/mysq
 l-connector-j-8.0.31.jar lib/mybatis-3.5.5.jar lib/spring-boot-starte
 r-websocket-2.7.5.jar lib/spring-messaging-5.3.23.jar lib/spring-webs
 ocket-5.3.23.jar lib/erupt-designer-entity-1.12.1.jar lib/freemarke
 r-2.3.31.jar lib/erupt-message-1.12.1.jar lib/lombok-1.18.24.jar

