package com.ydy.dingtalk.oa.entity;

import lombok.Data;

import java.util.List;

/**
 * Employee schedule request entity
 */
@Data
public class EmployeeScheduleRequest {
    /**
     * Employee names (optional)
     */
    private List<String> employees;
    
    /**
     * Start date (format: yyyy-MM-dd)
     */
    private String startDate;
    
    /**
     * End date (format: yyyy-MM-dd)
     */
    private String endDate;
} 