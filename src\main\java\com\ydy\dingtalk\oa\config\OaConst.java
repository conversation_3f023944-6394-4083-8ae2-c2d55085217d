package com.ydy.dingtalk.oa.config;

import xyz.erupt.annotation.config.Comment;

public interface OaConst {

    /**
     * 应用ID，与模块ID相同
     */
    String APPID = "dingtalk-oa";

    String APPNAME = "OA统计";

    String AK = "dingbhwaejud8gkyxklo";
    String SK = "_9H3Yt6Aq4IzckTXxrK_xIaMSCEQRqE4d_Su1YTQiY43utEjO3KwIjWCwn6NS_Md";

    @Comment("管理角色")
    String ROLE_MANAGER = "manager";

    @Comment("用户角色")
    String ROLE_USER = "user";

    @Comment("菜单入口")
    String MENU_ROOT = "$" + APPID;
}
