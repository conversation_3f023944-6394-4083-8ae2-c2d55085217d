package com.ydy.dingtalk.businessFlow.handler;

import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Component
public class BusiniessAgainTransfer implements OperationHandler<BusinessTransferOrder, Void> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @SneakyThrows
    @Transactional
    @Override
    public String exec(List<BusinessTransferOrder> data, Void unused, String[] param) {
        BusinessTransferOrder businessTransferOrder = data.get(0);
        getBusinessTransferOrder(businessTransferOrder);
        eruptDao.merge(businessTransferOrder);
        return "";
    }

    public void getBusinessTransferOrder(BusinessTransferOrder businessTransferOrder){
        String yunName = businessTransferOrder.getEffectiveFollowNum();
        String jpqlToSelectYun = "SELECT id FROM e_business_line_down_tag WHERE name = (:name)";
        Query queryToSelectYun = eruptDao.getEntityManager().createNativeQuery(jpqlToSelectYun);
        queryToSelectYun.setParameter("name", yunName);
        List yunList = queryToSelectYun.getResultList();
        long yunId = Long.parseLong(yunList.get(0).toString());
        String jpqlToSelectUser = "SELECT a.self_id FROM e_business_line_down_config a " +
                "JOIN e_line_down_user_tag b ON a.id = b.table_id " +
                "WHERE b.tag_id = " + yunId;
        Query queryToSelectUser = eruptDao.getEntityManager().createNativeQuery(jpqlToSelectUser);
        List resultList1 = queryToSelectUser.getResultList();
        List<String> onCompany = new ArrayList<>();
        for (Object o : resultList1) {
            setProperty(o.toString(),onCompany);
        }
        if(onCompany.size() == 0){
            throw new EruptApiErrorTip("没有可转单人!");
        }else if(onCompany.size() == 1){
            long l = Long.parseLong(onCompany.get(0));
            businessTransferOrder.setLineDownUserName(getUser(l));
            businessTransferOrder.setLineDownUser(onCompany.get(0));
        }else{
            String recentlyBusiness = getRecentlyBusiness();
            Random random = new Random();
            if(recentlyBusiness==null) {
                int randomIndex = random.nextInt(onCompany.size());
                String s = onCompany.get(randomIndex);
                String user = getUser(Long.parseLong(s));
                businessTransferOrder.setLineDownUserName(user);
                businessTransferOrder.setLineDownUser(s);
            }else {
                onCompany.remove(recentlyBusiness);
                if(onCompany.size() == 1){
                    String user = getUser(Long.parseLong(onCompany.get(0)));
                    businessTransferOrder.setLineDownUserName(user);
                    businessTransferOrder.setLineDownUser(onCompany.get(0));
                }else {
                    int randomIndex = random.nextInt(onCompany.size());
                    String s = onCompany.get(randomIndex);
                    String user = getUser(Long.parseLong(s));
                    businessTransferOrder.setLineDownUserName(user);
                    businessTransferOrder.setLineDownUser(s);
                }
            }
        }
    }

    public String getUser(Long key){
        String jpqlCompany = "SELECT name FROM e_upms_user WHERE id = "+key;
        Query queryCompany = eruptDao.getEntityManager().createNativeQuery(jpqlCompany);
        List onCompanys = queryCompany.getResultList();
        if(onCompanys.size()>0){
            return onCompanys.get(0).toString();
        }
        throw new EruptApiErrorTip("转单人不在公司内，请联系管理员!");
    }

    public void setProperty(String key,List<String> onCompany){
        String jpqlCompany = "SELECT self_id FROM e_business_line_down_config WHERE user_status = '0' and self_id = (:selfId)";
        Query queryCompany = eruptDao.getEntityManager().createNativeQuery(jpqlCompany);
        queryCompany.setParameter("selfId", key);
        List onCompanys = queryCompany.getResultList();

        String jpqlCompany1 = "select a.id FROM e_upms_user a JOIN e_upms_user_role b ON a.id = b.user_id JOIN e_upms_role c ON b.role_id = c.id WHERE a.id = " + Integer.parseInt(key) + " AND c.`name` = '商机流转管理-面销' ";
        Query queryCompany1 = eruptDao.getEntityManager().createNativeQuery(jpqlCompany1);
        List onCompanys1 = queryCompany1.getResultList();

        if(onCompanys.size()>0 && onCompanys1.size()>0){
            onCompany.add(key);
        }
    }

    public String getRecentlyBusiness(){
        String jpqlCompany = "SELECT line_down_user FROM e_business_trans_list WHERE business_status != '1' order by date desc limit 1";
        Query queryCompany = eruptDao.getEntityManager().createNativeQuery(jpqlCompany);
        List onCompanys = queryCompany.getResultList();
        if(onCompanys.size()>0){
            if(onCompanys.get(0) == null){
                return null;
            }
            return onCompanys.get(0).toString();
        }
        return null;
    }
}
