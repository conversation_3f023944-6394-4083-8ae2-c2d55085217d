package com.ydy.dingtalk.wxfriends.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.model.EruptUserByRoleView;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2023/9/22
 */
@Service
public class IsReadOnly implements Readonly.ReadonlyHandler {

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public boolean add(boolean add, String[] params) {
        //查询管理员用户
        boolean isAdd=true;
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Map<String, String> accountMap = AdminSearch.getAdmin();
        if(currentEruptUser!=null&&accountMap.containsKey(currentEruptUser.getAccount())){
            isAdd=false;
        }
        return isAdd;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        //查询管理员用户
        boolean isAdd=true;
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Map<String, String> accountMap = AdminSearch.getAdmin();
        if(currentEruptUser!=null&&accountMap.containsKey(currentEruptUser.getAccount())){
            isAdd=false;
        }
        return isAdd;
    }
}
