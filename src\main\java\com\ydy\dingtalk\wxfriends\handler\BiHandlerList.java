package com.ydy.dingtalk.wxfriends.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.bi.fun.EruptBiHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@Service
public class BiHandlerList implements EruptBiHandler {

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
        return CommonHandler.exprHandler(param,condition,expr);
    }

    @Override
    public void resultHandler(String param, Map<String, Object> condition, List<Map<String, Object>> result) {
        //返回结果处理
        //合计上次总量、当前总量、本周新增
        //上次总量总和
        AtomicReference<Integer> lastCountSum= new AtomicReference<>(0);
        //当前总量总和
        AtomicReference<Integer> thisCountSum= new AtomicReference<>(0);
        //本周新增总和
        AtomicReference<Integer> newAddSum= new AtomicReference<>(0);
        result.forEach(r->{
            lastCountSum.updateAndGet(v -> v + Integer.parseInt(String.valueOf(r.get("上次总量"))));
            thisCountSum.updateAndGet(v -> v + Integer.parseInt(String.valueOf(r.get("当前总量"))));
            newAddSum.updateAndGet(v -> v + Integer.parseInt(String.valueOf(r.get("本周新增"))));
        });
        //增肌自定义行
        HashMap<String, Object> totalSum = new HashMap<>();
        totalSum.put("日期","");
        totalSum.put("周数","");
        totalSum.put("团队","");
        totalSum.put("使用人","");
        totalSum.put("微信号","");
        totalSum.put("昵称","");
        totalSum.put("手机号","总计：");
        totalSum.put("上次总量",lastCountSum);
        totalSum.put("当前总量",thisCountSum);
        totalSum.put("本周新增",newAddSum);
        result.add(totalSum);
    }
}
