package com.ydy.dingtalk.businessFlow.selectdevice;

import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

@Component
public class BusinessChoseYunType implements ChoiceFetchHandler {

    @Resource
    private EruptDao eruptDao;

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        String jpql = "SELECT a.name FROM e_dict_item a " +
                "JOIN e_dict b ON a.erupt_dict_id = b.id " +
                "WHERE b.name = '云类型'";
        return getVlModels(list, jpql, eruptDao);
    }

    public static List<VLModel> getVlModels(List<VLModel> list, String jpql, EruptDao eruptDao) {
        Query query = eruptDao.getEntityManager().createNativeQuery(jpql);
        List resultList = query.getResultList();
        if (resultList.size()>0){
            for (Object o : resultList) {
                VLModel vlModel = new VLModel();
                vlModel.setLabel(o.toString());
                vlModel.setValue(o.toString());
                list.add(vlModel);
            }
        }
        return list;
    }
}
