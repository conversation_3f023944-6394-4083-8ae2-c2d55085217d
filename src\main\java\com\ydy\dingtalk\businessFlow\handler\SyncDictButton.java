package com.ydy.dingtalk.businessFlow.handler;

import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import com.ydy.dingtalk.businessFlow.entity.YunTypeTag;
import com.ydy.dingtalk.businessFlow.selectdevice.BusinessLineDownNameUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.List;

@Component
public class SyncDictButton implements OperationHandler<BusinessTransferOrder, Void> {

    @Resource
    private EruptDao eruptDao;



    @Override
    @Transactional
    public String exec(List<BusinessTransferOrder> data, Void unused, String[] param) {
        String sql = "SELECT a.name FROM e_dict_item a LEFT JOIN e_business_line_down_tag b ON a.name = b.name JOIN e_dict c ON c.id = a.erupt_dict_id WHERE b.name IS NULL AND c.name = '云类型'";
        Query query = eruptDao.getEntityManager().createNativeQuery(sql);
        List resultList = query.getResultList();
        if (resultList.size()>0){
            for (Object o : resultList) {
                String sql1 = "INSERT INTO e_business_line_down_tag (name) VALUES (:name)";
                eruptDao.getEntityManager().createNativeQuery(sql1)
                        .setParameter("name", o.toString())
                        .executeUpdate();
            }
        }
        return null;
    }
}
