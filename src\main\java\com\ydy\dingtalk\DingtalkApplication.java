package com.ydy.dingtalk;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import xyz.erupt.core.annotation.EruptScan;

@SpringBootApplication
@EruptScan
@EntityScan
//@EruptAttachmentUpload(CosStoreProxy.class)
//@EruptAttachmentUpload(MinioStoreProxy.class)
@EnableFeignClients
@EnableCaching
public class DingtalkApplication {

    public static void main(String[] args) {
        SpringApplication.run(DingtalkApplication.class, args);
    }

}
