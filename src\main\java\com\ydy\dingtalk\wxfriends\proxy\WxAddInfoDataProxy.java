package com.ydy.dingtalk.wxfriends.proxy;

import com.ydy.dingtalk.wxfriends.entity.WxAddInfo;
import com.ydy.dingtalk.wxfriends.entity.WxIdInfo;
import com.ydy.dingtalk.wxfriends.handler.AdminSearch;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.QueryExpression;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.toolkit.db.EruptResultMap;
import xyz.erupt.toolkit.utils.AssertUtils;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;
import xyz.erupt.upms.model.EruptOrg;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class WxAddInfoDataProxy implements DataProxy<WxAddInfo> {

    @Builder
    @Data
    static class LastCount{
        private Integer lastCount;
    }

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public void addBehavior(WxAddInfo wxAddInfo) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        if(currentEruptUser.getIsAdmin()){
            return;
        }
        //获取当前组织
        EruptOrg eruptOrg = currentEruptUser.getEruptOrg();
        if (eruptOrg == null) NotifyUtils.showErrorDialog("当前没有组织信息，无法添加");
        //设置团队
        wxAddInfo.setSubDeptId(String.valueOf(eruptOrg.getId()));
        //设置使用人
        wxAddInfo.setCurrUser(currentEruptUser.getName());
        //获取当前时间
        Calendar calendar = Calendar.getInstance();
        Date now = new Date();
        calendar.setTime(now);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String nowStr = simpleDateFormat.format(now);
        //设置年份
        wxAddInfo.setYear(nowStr.substring(0, 4));
        //设置日期
        wxAddInfo.setDate(nowStr);
        //设置周数
        wxAddInfo.setWeek(String.valueOf(calendar.get(Calendar.WEEK_OF_YEAR)));

    }


    @Override
    public void beforeAdd(WxAddInfo wxAddInfo) {
        //获取表名
        String table = EruptUtil.getTable(WxAddInfo.class);
        //设置创建时间
        wxAddInfo.setCreateTime(LocalDateTime.now());
        //设置创建人
        wxAddInfo.setCreateBy(eruptUserService.getCurrentEruptUser().getName());
        //拆分昵称-微信号-手机号
        if (wxAddInfo.getWxMobile().contains("|")) {
            String[] data = wxAddInfo.getWxMobile().split("\\|");
            //昵称
            wxAddInfo.setNickname(data[0]);
            //微信号
            wxAddInfo.setWxId(data[1]);
            //手机号
            wxAddInfo.setPhone(data[2]);
        }
        //判读是否已有相同的记录,一周一个手机号只能提交一次
        String phoneSql = String.format("select * from `%s` where phone='%s'", table, wxAddInfo.getPhone());
        List<WxAddInfo> wxAddInfoList = EruptDaoUtils.selectOnes(phoneSql, WxAddInfo.class);
        if (wxAddInfoList.size() > 0) {
            //看这个微信号选择的周是否已经提交了，提交则只能去修改，不能新增
            wxAddInfoList.forEach(wxAdd -> {
                if (wxAdd.getWeek().equals(wxAddInfo.getWeek())) {
                    NotifyUtils.showErrorDialog("一周可提交一次!");
                }
            });
        }
        //获取上次总量
        Integer lastCount = lastCountByPhone(wxAddInfo.getPhone());
        //获取当前总量
        Integer currCount = wxAddInfo.getCurrCount();
        //设置上次总量
        wxAddInfo.setLastCount(lastCount);
        //设置本周新增
        wxAddInfo.setThisWeekCount(currCount - lastCount);
    }

    @Override
    public void beforeUpdate(WxAddInfo wxAddInfo) {
        //获取表名
        String table = EruptUtil.getTable(WxAddInfo.class);
        //设置修改时间
        wxAddInfo.setUpdateTime(LocalDateTime.now());
        //设置修改人
        wxAddInfo.setUpdateBy(eruptUserService.getCurrentEruptUser().getName());
        //拆分昵称-微信号-手机号
        if (wxAddInfo.getWxMobile().contains("|")) {
            String[] data = wxAddInfo.getWxMobile().split("\\|");
            //昵称
            wxAddInfo.setNickname(data[0]);
            //微信号
            wxAddInfo.setWxId(data[1]);
            //手机号
            wxAddInfo.setPhone(data[2]);
        }
        //获取上次总量
        Integer lastCount = lastCountByPhone(wxAddInfo.getPhone());
        //获取当前总量
        Integer currCount = wxAddInfo.getCurrCount();
        //设置上次总量
        wxAddInfo.setLastCount(lastCount);
        //设置本周新增
        wxAddInfo.setThisWeekCount(currCount - lastCount);
    }

    //获取上次总量
    public Integer lastCountByPhone(String phone) {
        //获取表名
        String table = EruptUtil.getTable(WxAddInfo.class);
        //查询sql
        String lastCountSql = String.format("SELECT IFNULL(curr_count,0) lastCount FROM `%s` WHERE `phone`='%s' ORDER BY `date` DESC LIMIT 1", table, phone);
        LastCount lastCountObj = EruptDaoUtils.selectOne(lastCountSql,LastCount.class);
        Integer lastCount = lastCountObj==null?0:lastCountObj.getLastCount();
        return lastCount;
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        Map<String, String> accountMap = AdminSearch.getAdmin();
        if(!(currentEruptUser!=null&&accountMap.containsKey(currentEruptUser.getAccount()))){
            conditions.add(new Condition("currUser",currentEruptUser.getName(),QueryExpression.EQ));

        }
        return null;
    }
}
