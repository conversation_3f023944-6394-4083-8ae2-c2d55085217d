package com.ydy.dingtalk.outVisit.config;

import com.ydy.dingtalk.outVisit.entity.OutVisitManage;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import xyz.erupt.core.annotation.EruptScan;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.dingtalk.core.DingPlatformService;
import xyz.erupt.idaas.enums.PlatformType;
import xyz.erupt.idaas.service.IDaasService;
import xyz.erupt.upms.microapp.MicroApp;
import xyz.erupt.upms.microapp.MicroMenu;
import xyz.erupt.upms.microapp.MicroRole;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2021/3/28 18:51
 */
@Configuration
@ComponentScan
@EntityScan
@EruptScan
@Component
@EnableConfigurationProperties
@Slf4j
@MicroApp(name = OutVisitConst.APPNAME, module = OutVisitConst.APPID,
        defaults = @MicroApp.Default(role = OutVisitConst.ROLE_SALES),
        roles = {
                @MicroRole(code = OutVisitConst.ROLE_SALES, name = "销售", home = "OutVisitManage", sort = 1,
                        menus = {
                                @MicroMenu(code = OutVisitConst.MENU_ROOT),
                                @MicroMenu(erupt = OutVisitManage.class)
                        }),
                @MicroRole(code = OutVisitConst.ROLE_MANAGER, name = "管理员",home = "OutVisitManage", sort = 100,
                        menus = {
                                @MicroMenu(code = OutVisitConst.MENU_ROOT),
                                @MicroMenu(erupt = OutVisitManage.class)
                        }
                )
        })
public class OutVisitConfig implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(OutVisitConfig.class);
    }

    @Resource
    private IDaasService iDaasService;

    @Resource
    private DingPlatformService dingPlatformService;

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder()
                .name("ydy-outvisit")
                .moduleCode(OutVisitConst.APPID)
                .moduleTitle(OutVisitConst.APPNAME)
                .moduleSort(1)
                .autoCreate(true)
                .build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        List<MetaMenu> menus = new ArrayList<>();
        menus.add(MetaMenu.createRootMenu(OutVisitConst.MENU_ROOT, OutVisitConst.APPNAME, "fa fa-briefcase", 1));
        menus.add(MetaMenu.createSimpleMenu(OutVisitConst.MENU_STAT, "数据统计", OutVisitConst.MENU_STAT, menus.get(0), 1, "bi"));
        menus.add(MetaMenu.createEruptClassMenu(OutVisitManage.class, menus.get(0), 2));
        return menus;
    }

    @Override
    public void initFun() {
        iDaasService.register(OutVisitConst.APPID,
                OutVisitConst.APPNAME, PlatformType.DINGTALK_ACCOUNT,
                OutVisitConst.AK, OutVisitConst.SK,
                "http://ding-platform.yundingyun.net/sns/auth/" + OutVisitConst.APPID,
                "/#/build/" + OutVisitManage.class.getSimpleName());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dingPlatformService.registerDefaultAccessKey(OutVisitConst.AK, OutVisitConst.SK);
    }
}
