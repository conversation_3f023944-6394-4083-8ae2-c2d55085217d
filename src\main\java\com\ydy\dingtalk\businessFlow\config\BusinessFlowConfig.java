package com.ydy.dingtalk.businessFlow.config;

import com.ydy.dingtalk.businessFlow.entity.BusinessLineDownConfig;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import xyz.erupt.core.annotation.EruptScan;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.dingtalk.core.DingPlatformService;
import xyz.erupt.idaas.enums.PlatformType;
import xyz.erupt.idaas.service.IDaasService;
import xyz.erupt.upms.microapp.MicroApp;
import xyz.erupt.upms.microapp.MicroMenu;
import xyz.erupt.upms.microapp.MicroRole;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2021/3/28 18:51
 */
@Configuration
@ComponentScan
@EntityScan
@EruptScan
@Component
@EnableConfigurationProperties
@Slf4j
@MicroApp(name = BusinessFlowConst.APPNAME, module = BusinessFlowConst.APPID,
        defaults = @MicroApp.Default(role = BusinessFlowConst.ROLE_ONLINE),
        roles = {
                @MicroRole(code = BusinessFlowConst.ROLE_ONLINE, name = "电销", home = "BusinessTransferOrder", sort = 1,
                        menus = {
                                @MicroMenu(code = BusinessFlowConst.MENU_ROOT),
                                @MicroMenu(erupt = BusinessTransferOrder.class),
                                @MicroMenu(erupt = BusinessLineDownConfig.class)
                        }),
                @MicroRole(code = BusinessFlowConst.ROLE_LINEDOWN, name = "面销", home = "BusinessTransferOrder", sort = 2,
                        menus = {
                                @MicroMenu(code = BusinessFlowConst.MENU_ROOT),
                                @MicroMenu(erupt = BusinessTransferOrder.class),
                                @MicroMenu(erupt = BusinessLineDownConfig.class)
                        }),
                @MicroRole(code = BusinessFlowConst.ROLE_MANAGE, name = "主管", home = "BusinessTransferOrder", sort = 3,
                        menus = {
                                @MicroMenu(code = BusinessFlowConst.MENU_ROOT),
                                @MicroMenu(erupt = BusinessTransferOrder.class),
                                @MicroMenu(erupt = BusinessLineDownConfig.class)
                        }),
                @MicroRole(code = BusinessFlowConst.ROLE_ADMIN, name = "管理员",home = "BusinessTransferOrder", sort = 100,
                        menus = {
                                @MicroMenu(code = BusinessFlowConst.MENU_ROOT),
                                @MicroMenu(erupt = BusinessTransferOrder.class),
                                @MicroMenu(erupt = BusinessLineDownConfig.class)
                        }
                )
        })
public class BusinessFlowConfig implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(BusinessFlowConfig.class);
    }

    @Resource
    private IDaasService iDaasService;

    @Resource
    private DingPlatformService dingPlatformService;

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder()
                .name("ydy-businessflow")
                .moduleCode(BusinessFlowConst.APPID)
                .moduleTitle(BusinessFlowConst.APPNAME)
                .moduleSort(1)
                .autoCreate(true)
                .build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        List<MetaMenu> menus = new ArrayList<>();
        menus.add(MetaMenu.createRootMenu(BusinessFlowConst.MENU_ROOT, BusinessFlowConst.APPNAME, "fa fa-briefcase", 1));
        menus.add(MetaMenu.createSimpleMenu(BusinessFlowConst.MENU_STAT, "数据统计", BusinessFlowConst.MENU_STAT, menus.get(0), 1, "bi"));
        menus.add(MetaMenu.createEruptClassMenu(BusinessTransferOrder.class, menus.get(0), 2));
        menus.add(MetaMenu.createEruptClassMenu(BusinessLineDownConfig.class, menus.get(0), 3));
        return menus;
    }

    @Override
    public void initFun() {
        iDaasService.register(BusinessFlowConst.APPID,
                BusinessFlowConst.APPNAME, PlatformType.DINGTALK_ACCOUNT,
                BusinessFlowConst.AK, BusinessFlowConst.SK,
                "http://ding-platform.yundingyun.net/sns/auth/" + BusinessFlowConst.APPID,
                "/#/build/" + BusinessTransferOrder.class.getSimpleName());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dingPlatformService.registerDefaultAccessKey(BusinessFlowConst.AK, BusinessFlowConst.SK);
    }
}
