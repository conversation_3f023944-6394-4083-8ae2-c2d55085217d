package com.ydy.dingtalk.outVisit.proxy;

import com.ydy.dingtalk.businessFlow.selectdevice.BusinessChoseYunType;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Component
public class SelectVisitUser implements ChoiceFetchHandler {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;


    @Override
    public List<VLModel> fetch(String[] params) {
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        boolean res = true;
        List<VLModel> list = new ArrayList<>();
        for (EruptRole role : roles) {
            if(role.getName().equals("每日客户跟进-管理员")){
                res = false;
            }
        }
        if(eruptUserService.getCurrentEruptUser().getId() == 1){
            res = false;
        }
        String jpql;
        if(!res){
//            jpql = "select a.name from e_upms_user a JOIN e_upms_user_role b ON a.id = b.user_id JOIN e_upms_role c ON b.role_id = c.id WHERE c.name = '每日客户跟进-销售'";
            jpql = "select a.visit_user_name from tb_out_manage a group by a.visit_user_name";
            return BusinessChoseYunType.getVlModels(list, jpql, eruptDao);
        }
        VLModel vlModel = new VLModel();
        vlModel.setLabel(eruptUserService.getCurrentEruptUser().getName());
        vlModel.setValue(eruptUserService.getCurrentEruptUser().getName());
        list.add(vlModel);
        return list;
    }
}
