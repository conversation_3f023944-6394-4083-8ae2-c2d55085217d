package com.ydy.dingtalk.businessFlow.proxy;

import com.ydy.dingtalk.businessFlow.entity.BusinessLineDownConfig;
import com.ydy.dingtalk.businessFlow.entity.BusinessTransferOrder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.QueryExpression;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class BusinessLineDownProxy implements DataProxy<BusinessLineDownConfig> {
    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptDao eruptDao;


    @Override
    public void addBehavior(BusinessLineDownConfig outVisitManage) {
        if(selectAdmins()){
            outVisitManage.setSelfId(eruptUserService.getCurrentEruptUser().getId().toString());
            outVisitManage.setSelfName(eruptUserService.getCurrentEruptUser().getName());
        }
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if(eruptUserService.getCurrentEruptUser().getId() == 1){
            return null;
        }
        boolean res = true;
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        for (EruptRole role : roles) {
            if(role.getName().equals("商机流转管理-管理员")){
                return null;
            }
            if(role.getName().equals("商机流转管理-主管")){
                res = false;
            }
        }
        if(res){
            conditions.add(new Condition("selfId",eruptUserService.getCurrentEruptUser().getId().toString(), QueryExpression.EQ));
        }else {
            List<Long> idList = new ArrayList<>();
            getOrgUserList(eruptUserService, eruptDao,idList);
            conditions.add(new Condition("selfId",idList,QueryExpression.IN));
        }
        return null;
    }

    public static void getOrgUserList(EruptUserService eruptUserService, EruptDao eruptDao,List<Long> idList) {
        Long id = eruptUserService.getCurrentEruptUser().getEruptOrg().getId();
        String jpql = "SELECT id FROM EruptUser WHERE eruptOrg.id = :eruptOrgId";
        Query query = eruptDao.getEntityManager().createQuery(jpql);
        query.setParameter("eruptOrgId", id);
        List resultList = query.getResultList();
        for (Object o : resultList) {
            idList.add(Long.parseLong(o.toString()));
        }
    }

    @Override
    public void beforeAdd(BusinessLineDownConfig businessLineDownConfig){
            String jpql = "SELECT id FROM BusinessLineDownConfig WHERE selfName = :eruptOrgId";
            Query query = eruptDao.getEntityManager().createQuery(jpql);
            query.setParameter("eruptOrgId", businessLineDownConfig.getSelfName());
            List resultList = query.getResultList();
            if (resultList.size() > 0){
                throw new EruptApiErrorTip("已存在面销设置，只允许添加一次!");
            }
            String jpql1 = "SELECT id FROM EruptUser WHERE name = :name";
            Query query1 = eruptDao.getEntityManager().createQuery(jpql1);
            query1.setParameter("name", businessLineDownConfig.getSelfName());
            List resultList1 = query1.getResultList();
            if (resultList1.size() == 0){
                throw new EruptApiErrorTip("面销不存在，请检查组织!");
            }
            businessLineDownConfig.setSelfId(resultList1.get(0).toString());
    }



    private boolean selectAdmins() {
        boolean controllerToLimit = true;
        if(eruptUserService.getCurrentEruptUser().getId()==1){
            controllerToLimit = false;
        }else {
            Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
            for (EruptRole role : roles) {
                if (role.getName().equals("商机流转管理-管理员") || role.getName().equals("商机流转管理-主管")) {
                    controllerToLimit = false;
                    break;
                }
            }
        }
        return controllerToLimit;
    }
}
