package com.ydy.dingtalk.wxfriends.handler;

import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.model.EruptUser;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/7
 */
public class AdminSearch {
    public static Map<String, String> getAdmin(){
        String mangerSql=String.format("SELECT * FROM e_upms_user where id in(SELECT user_id FROM e_upms_user_role where role_id=(SELECT id from e_upms_role where code='%s')) or is_admin=1", "module-wx-friends-manager");
        List<EruptUser> eruptUsers = EruptDaoUtils.selectOnes(mangerSql, EruptUser.class);
        Map<String, String> accountMap = eruptUsers.stream().collect(Collectors.toMap(
                EruptUser::getAccount,
                EruptUser::getName,
                (e1, e2) -> e1
        ));
        return accountMap;
    }

    // 每日跟劲
    public static Map<String, String> getIAdminOut(){
        String mangerSql=String.format("SELECT * FROM e_upms_user where id in(SELECT user_id FROM e_upms_user_role where role_id=(SELECT id from e_upms_role where code='%s')) or is_admin=1", "module-wx-outvisit-manager");
        List<EruptUser> eruptUsers = EruptDaoUtils.selectOnes(mangerSql, EruptUser.class);
        Map<String, String> accountMap = eruptUsers.stream().collect(Collectors.toMap(
                EruptUser::getAccount,
                EruptUser::getName,
                (e1, e2) -> e1
        ));
        return accountMap;
    }
}
