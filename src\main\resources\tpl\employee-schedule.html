<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工日程表</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 100%;
            padding: 20px;
            box-sizing: border-box;
        }
        .header {
            background-color: #fff;
            padding: 16px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .el-form-item__label {
            font-weight: 500;
        }
        .el-button--primary {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        .el-button--primary:hover,
        .el-button--primary:focus {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        .el-table {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .el-table th {
            background-color: #f5f7fa !important;
            color: #606266;
            font-weight: 500;
            border-bottom: 1px solid #ebeef5;
        }
        .el-table--border td, .el-table--border th {
            border-right: 1px solid #ebeef5;
        }
        .el-table tr:hover > td {
            background-color: #f5f7fa !important;
        }
        .el-table--striped .el-table__body tr.el-table__row--striped td {
            background: #fafafa;
        }
        .el-pagination {
            margin-top: 20px;
            text-align: right;
            padding: 10px 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
        }
        .schedule-table th, .schedule-table td {
            border: 1px solid #ebeef5;
            padding: 12px 8px;
            text-align: center;
        }
        .schedule-table th {
            background-color: #f5f7fa;
            font-weight: 500;
            color: #606266;
        }
        .schedule-item {
            margin-bottom: 5px;
            padding: 8px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .schedule-item:last-child {
            margin-bottom: 0;
        }
        .外出 {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .出差 {
            background-color: #f0f9eb;
            border-left: 3px solid #67c23a;
        }
        .培训交流 {
            background-color: #fff8e6;
            border-left: 3px solid #e6a23c;
        }
        .外部参会 {
            background-color: #fef0f0;
            border-left: 3px solid #f56c6c;
        }
        .内部会议 {
            background-color: #f5f5f5;
            border-left: 3px solid #909399;
        }
        .time-period {
            font-size: 12px;
            color: #666;
            margin-right: 5px;
        }
        .content {
            font-size: 13px;
            word-break: break-all;
        }
        /* 操作按钮样式 */
        .action-button {
            color: #1890ff;
            margin-right: 10px;
            cursor: pointer;
        }
        .action-button:hover {
            color: #40a9ff;
        }
        .delete-button {
            color: #f56c6c;
        }
        .delete-button:hover {
            color: #f78989;
        }
        .morning-schedule {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .afternoon-schedule {
            background-color: #f0f9eb;
            border-left: 3px solid #67c23a;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
        
            <!-- 搜索区域 -->
            <el-form :inline="true" size="small">
                <el-form-item label="员工姓名">
                    <el-select
                        v-model="selectedEmployees"
                        multiple
                        filterable
                        placeholder="请选择员工"
                        style="width: 300px"
                    >
                        <el-option
                            v-for="item in employeeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="日期范围">
                    <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 300px"
                        :picker-options="datePickerOptions"
                    >
                    </el-date-picker>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="fetchData">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="success" @click="downloadSchedule" :disabled="!hasScheduleData">下载</el-button>
                </el-form-item>
            </el-form>
        </div>
        
        <!-- 无数据提示 -->
        <div v-if="!hasScheduleData" style="text-align: center; padding: 50px; background-color: #fff; border-radius: 4px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);">
            <el-empty description="请选择员工查询日程数据"></el-empty>
        </div>
        
        <!-- 表格区域，只在有数据时显示 -->
        <el-table
            v-if="hasScheduleData"
            v-loading="loading"
            :data="scheduleData"
            border
            stripe
            style="width: 100%; background-color: #fff;"
            :span-method="arraySpanMethod"
        >
            <el-table-column
                prop="employeeName"
                label="姓名"
                width="120"
                align="center"
                fixed="left"
            >
            </el-table-column>
            
            <!-- 动态生成日期列 -->
            <el-table-column
                v-for="(date, index) in dates"
                :key="index"
                :label="formatDate(date)"
                align="center"
                width="180"
            >
                <template slot-scope="scope">
                    <div v-if="scope.row.dateSchedules && scope.row.dateSchedules[date]">
                        <!-- 上午日程 -->
                        <div v-if="getTimePeriodSchedules(scope.row.dateSchedules[date], '上午').length > 0" 
                             class="schedule-item morning-schedule">
                            <div class="time-period">[上午]</div>
                            <div class="content">
                                <template v-for="(item, idx) in getTimePeriodSchedules(scope.row.dateSchedules[date], '上午')">
                                    <span :key="'morning-'+idx">
                                        <strong>{{ item.type }}:</strong> {{ item.content }}
                                        <br v-if="idx < getTimePeriodSchedules(scope.row.dateSchedules[date], '上午').length - 1" />
                                    </span>
                                </template>
                            </div>
                        </div>
                        
                        <!-- 下午日程 -->
                        <div v-if="getTimePeriodSchedules(scope.row.dateSchedules[date], '下午').length > 0" 
                             class="schedule-item afternoon-schedule">
                            <div class="time-period">[下午]</div>
                            <div class="content">
                                <template v-for="(item, idx) in getTimePeriodSchedules(scope.row.dateSchedules[date], '下午')">
                                    <span :key="'afternoon-'+idx">
                                        <strong>{{ item.type }}:</strong> {{ item.content }}
                                        <br v-if="idx < getTimePeriodSchedules(scope.row.dateSchedules[date], '下午').length - 1" />
                                    </span>
                                </template>
                            </div>
                        </div>
                        
                        <!-- 其他时间段的日程 -->
                        <div v-for="(item, itemIndex) in getOtherTimePeriodSchedules(scope.row.dateSchedules[date])" 
                             :key="itemIndex"
                             :class="['schedule-item', item.type]">
                            <div class="time-period">[{{ item.timePeriod }}]</div>
                            <div class="content">
                                <strong>{{ item.type }}:</strong> {{ item.content }}
                            </div>
                        </div>
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <!-- 引入相关JS库 -->
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    employeeOptions: [],
                    selectedEmployees: [],
                    dateRange: [],
                    dates: [],
                    scheduleData: [],
                    datePickerOptions: {
                        onPick: ({ maxDate, minDate }) => {
                            if (maxDate && minDate) {
                                // 计算选择的日期范围
                                const diffDays = (new Date(maxDate).getTime() - new Date(minDate).getTime()) / (1000 * 60 * 60 * 24);
                                if (diffDays > 30) {
                                    this.$message.warning('日期范围不能超过31天');
                                    this.dateRange = [];
                                }
                            }
                        }
                    }
                }
            },
            computed: {
                hasScheduleData() {
                    return this.scheduleData && this.scheduleData.length > 0;
                }
            },
            created() {
                // 设置默认日期范围为本周
                const today = new Date();
                const dayOfWeek = today.getDay() || 7; // 获取星期几，如果是0（周日）则设为7
                const monday = new Date(today);
                monday.setDate(today.getDate() - dayOfWeek + 1); // 设置为本周一
                
                const sunday = new Date(today);
                sunday.setDate(today.getDate() + (7 - dayOfWeek)); // 设置为本周日
                
                this.dateRange = [this.formatDateToString(monday), this.formatDateToString(sunday)];
                
                // 仅加载员工下拉选项，不自动加载日程数据
                this.fetchEmployeeOptions();
            },
            methods: {
                // 获取员工下拉选项
                async fetchEmployeeOptions() {
                    this.loading = true;
                    try {
                        // 这里应该调用后端API获取员工列表
                        const response = await axios.get('/erupt-api/oa/employeeSchedule', {
                            params: { getEmployeesOnly: true }
                        });
                        
                        console.log('员工列表响应:', response.data);
                        
                        // 修改判断条件，使用 status === "SUCCESS" 而不是 success
                        if (response.data.status === "SUCCESS") {
                            this.employeeOptions = response.data.data.map(name => ({
                                label: name,
                                value: name
                            }));
                        } else {
                            console.error('获取员工列表失败:', response.data.message);
                            this.$message.error('获取员工列表失败: ' + (response.data.message || '未知错误'));
                        }
                    } catch (error) {
                        console.error('获取员工列表出错:', error);
                        if (error.response) {
                            console.error('错误响应:', error.response.data);
                            console.error('错误状态:', error.response.status);
                        }
                        this.$message.error('获取员工列表出错: ' + (error.message || '未知错误'));
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 获取日程数据
                async fetchData() {
                    // 添加员工选择验证
                    if (!this.selectedEmployees || this.selectedEmployees.length === 0) {
                        this.$message.warning('请至少选择一名员工');
                        return;
                    }
                    
                    // 添加日期范围验证
                    if (this.dateRange && this.dateRange.length === 2) {
                        const startDate = new Date(this.dateRange[0]);
                        const endDate = new Date(this.dateRange[1]);
                        const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                        
                        if (diffDays > 30) {
                            this.$message.warning('日期范围不能超过31天');
                            return;
                        }
                    }
                    
                    this.loading = true;
                    try {
                        // 准备请求参数
                        const params = {
                            employees: this.selectedEmployees,
                            startDate: this.dateRange[0],
                            endDate: this.dateRange[1]
                        };
                        
                        // 调试输出
                        console.log('请求参数:', params);
                        
                        // 调用后端API
                        const response = await axios.post('/erupt-api/oa/employeeSchedule', params);
                        
                        // 调试输出
                        console.log('API响应:', response.data);
                        
                        // 修改判断条件，使用 status === "SUCCESS" 而不是 success
                        if (response.data.status === "SUCCESS") {
                            const { dates, schedules } = response.data.data;
                            this.dates = dates;
                            this.scheduleData = schedules;
                        } else {
                            console.error('错误信息:', response.data.message);
                            this.$message.error('获取日程数据失败: ' + (response.data.message || '未知错误'));
                        }
                    } catch (error) {
                        console.error('获取日程数据出错详情:', error);
                        console.error('错误配置:', error.config);
                        if (error.response) {
                            console.error('错误响应:', error.response.data);
                            console.error('错误状态:', error.response.status);
                        }
                        this.$message.error('获取日程数据出错: ' + (error.message || '未知错误'));
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 重置表单
                resetForm() {
                    this.selectedEmployees = [];
                    
                    // 重置为本周日期
                    const today = new Date();
                    const dayOfWeek = today.getDay() || 7;
                    const monday = new Date(today);
                    monday.setDate(today.getDate() - dayOfWeek + 1);
                    
                    const sunday = new Date(today);
                    sunday.setDate(today.getDate() + (7 - dayOfWeek));
                    
                    this.dateRange = [this.formatDateToString(monday), this.formatDateToString(sunday)];
                    
                    // 清空数据而不是重新加载
                    this.dates = [];
                    this.scheduleData = [];
                },
                
                // 新增：下载日程表功能
                downloadSchedule() {
                    if (!this.hasScheduleData) {
                        this.$message.warning('没有可下载的数据');
                        return;
                    }
                    
                    try {
                        // 生成CSV内容
                        let csvContent = '\uFEFF'; // BOM用于正确显示中文
                        
                        // 生成表头
                        let headers = ['姓名'];
                        this.dates.forEach(date => {
                            headers.push(this.formatDate(date));
                        });
                        csvContent += headers.join(',') + '\n';
                        
                        // 生成数据行
                        this.scheduleData.forEach(employee => {
                            let row = [employee.employeeName];
                            
                            this.dates.forEach(date => {
                                let cellContent = '';
                                if (employee.dateSchedules && employee.dateSchedules[date] && employee.dateSchedules[date].length > 0) {
                                    let schedules = employee.dateSchedules[date].map(item => 
                                        `[${item.timePeriod}] ${item.type}: ${item.content}`
                                    );
                                    cellContent = '"' + schedules.join('\n') + '"';
                                } else {
                                    cellContent = '-';
                                }
                                row.push(cellContent);
                            });
                            
                            csvContent += row.join(',') + '\n';
                        });
                        
                        // 创建下载链接
                        const encodedUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);
                        const link = document.createElement('a');
                        link.setAttribute('href', encodedUri);
                        
                        // 生成文件名：员工日程表_开始日期_结束日期.csv
                        const startDate = this.dateRange[0] || this.formatDateToString(new Date());
                        const endDate = this.dateRange[1] || this.formatDateToString(new Date());
                        link.setAttribute('download', `员工日程表_${startDate}_${endDate}.csv`);
                        
                        // 触发下载
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        this.$message.success('下载成功');
                    } catch (error) {
                        console.error('下载出错:', error);
                        this.$message.error('下载失败: ' + error.message);
                    }
                },
                
                // 日期格式化：yyyy-MM-dd 转为 MM月dd日
                formatDate(dateStr) {
                    const date = new Date(dateStr);
                    return `${date.getMonth() + 1}月${date.getDate()}日`;
                },
                
                // 日期转字符串：日期对象转为 yyyy-MM-dd
                formatDateToString(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                },
                
                // 表格合并单元格
                arraySpanMethod({ row, column, rowIndex, columnIndex }) {
                    // 只合并姓名列
                    if (columnIndex === 0) {
                        if (rowIndex === 0) {
                            return {
                                rowspan: 1,
                                colspan: 1
                            };
                        }
                    }
                },
                
                // 获取特定时间段的日程
                getTimePeriodSchedules(dateSchedules, timePeriod) {
                    if (!dateSchedules) return [];
                    return dateSchedules.filter(item => item.timePeriod.includes(timePeriod));
                },
                
                // 获取不是上午和下午的其他时间段日程
                getOtherTimePeriodSchedules(dateSchedules) {
                    if (!dateSchedules) return [];
                    return dateSchedules.filter(item => !item.timePeriod.includes('上午') && !item.timePeriod.includes('下午'));
                }
            }
        });
    </script>
</body>
</html> 