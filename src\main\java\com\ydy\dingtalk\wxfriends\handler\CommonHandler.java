package com.ydy.dingtalk.wxfriends.handler;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */
public class CommonHandler {

    public static Map<String,String> initMap(){
        HashMap<String, String> result = new HashMap<>();
        result.put("weekSearch","t2.week");
        result.put("peopleSearch","t2.curr_user");
        result.put("monthSearch","t2.date");
        result.put("teamSearch","t2.sub_dept_id");
        result.put("mobileSearch","t2.wx_mobile");
        result.put("dateSearch","t2.date");
        return result;
    }

    public static String exprHandler(String param, Map<String, Object> condition, String expr){
        StringBuilder sb = new StringBuilder();
        Map<String, String> initMap = initMap();
        sb.append(" where 1=1 ");
        condition.forEach((key, value) -> {
            if (value != null && !key.contains("__")) {
                switch (key) {
                    case "weekSearch":
                    case "teamSearch":
                        String tr = String.valueOf(value);
                        value=tr.substring(0,tr.indexOf("."));
                        sb.append("and ").append(initMap.get(key)).append("=").append("'").append(value).append("'");
                        break;
                    case "monthSearch":
                        sb.append("and ").append(initMap.get(key)).append(" like").append("'").append(value).append("%").append("'");
                        break;
                    default:
                        sb.append("and ").append(initMap.get(key)).append("=").append("'").append(value).append("'");
                        break;
                }
            }
        });
        String conditionSql = sb.toString();
        return expr.replace("#REPLACE", conditionSql);
    }
}
