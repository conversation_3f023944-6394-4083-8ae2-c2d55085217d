package com.ydy.dingtalk.wxfriends.controller;

import com.google.gson.internal.LinkedTreeMap;
import com.ydy.dingtalk.wxfriends.entity.WxIdInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.annotation.EruptRouter;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.util.EruptUtil;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.toolkit.remote.RemoteProxyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.SqlUtils;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@RestController
public class WxStaticController {

    @Resource
    private EruptUserService eruptUserService;

    @RequestMapping("erupt-api/get/wxInfoPhone")
    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public EruptApiModel getWxInfoPhone() {
        //查询当前
        EruptUser currentEruptUser = eruptUserService.getCurrentEruptUser();
        ArrayList<LinkedTreeMap> result = new ArrayList<>();
        String table = EruptUtil.getTable(WxIdInfo.class);
        String phoneSql=String.format("select * from `%s` where curr_user='%s'",table,currentEruptUser.getName());
        if(currentEruptUser.getIsAdmin()){
            phoneSql=String.format("select * from `%s`",table);
        }
        List<WxIdInfo> wxIdInfoList = EruptDaoUtils.selectOnes(phoneSql, WxIdInfo.class);
        if (ObjectUtils.isNotEmpty(wxIdInfoList)) {
            wxIdInfoList.forEach(v -> {
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("code",v.getNickname()+"|"+ v.getWechatId()+"|"+v.getMobile());
                map.put("name", "昵称:"+v.getNickname()+"  微信号:"+v.getWechatId()+"  手机号:"+v.getMobile());
                result.add(map);
            });
        }
        return EruptApiModel.successApi(result);
    }


}
