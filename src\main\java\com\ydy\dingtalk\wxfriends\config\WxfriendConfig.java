package com.ydy.dingtalk.wxfriends.config;

import com.ydy.dingtalk.wxfriends.entity.WxAddInfo;
import com.ydy.dingtalk.wxfriends.entity.WxIdInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import xyz.erupt.core.annotation.EruptScan;
import xyz.erupt.core.module.EruptModule;
import xyz.erupt.core.module.EruptModuleInvoke;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.core.module.ModuleInfo;
import xyz.erupt.dingtalk.core.DingPlatformService;
import xyz.erupt.idaas.enums.PlatformType;
import xyz.erupt.idaas.service.IDaasService;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.upms.microapp.MicroApp;
import xyz.erupt.upms.microapp.MicroMenu;
import xyz.erupt.upms.microapp.MicroRole;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2021/3/28 18:51
 */
@Configuration
@ComponentScan
@EntityScan
@EruptScan
@Component
@EnableConfigurationProperties
@Slf4j
@MicroApp(name = WxfriendConst.APPNAME, module = WxfriendConst.APPID,
        defaults = @MicroApp.Default(role = WxfriendConst.ROLE_SALES),
        roles = {
                @MicroRole(code = WxfriendConst.ROLE_SALES, name = "销售", home = "WxAddInfo", sort = 1,
                        menus = {
                                @MicroMenu(code = WxfriendConst.MENU_ROOT),
                                @MicroMenu(erupt = WxAddInfo.class),
                                @MicroMenu(erupt = WxIdInfo.class)
                        }),
                @MicroRole(code = WxfriendConst.ROLE_MANAGER, name = "管理员", home = WxfriendConst.MENU_STAT, sort = 100,
                        menus = {
                                @MicroMenu(code = WxfriendConst.MENU_ROOT),
                                @MicroMenu(code = WxfriendConst.MENU_STAT),
                                @MicroMenu(erupt = WxAddInfo.class),
                                @MicroMenu(erupt = WxIdInfo.class)
                        }
                )
        })
public class WxfriendConfig implements EruptModule, ApplicationRunner {

    static {
        EruptModuleInvoke.addEruptModule(WxfriendConfig.class);
    }

    @Resource
    private IDaasService iDaasService;

    @Resource
    private DingPlatformService dingPlatformService;

    @Override
    public ModuleInfo info() {
        return ModuleInfo.builder()
                .name("ydy-wxfriends")
                .moduleCode(WxfriendConst.APPID)
                .moduleTitle(WxfriendConst.APPNAME)
                .moduleSort(1)
                .autoCreate(true)
                .build();
    }

    @Override
    public List<MetaMenu> initMenus() {
        List<MetaMenu> menus = new ArrayList<>();
        menus.add(MetaMenu.createRootMenu(WxfriendConst.MENU_ROOT, WxfriendConst.APPNAME, "fa fa-wechat", 1));
        menus.add(MetaMenu.createSimpleMenu(WxfriendConst.MENU_STAT, "数据统计", WxfriendConst.MENU_STAT, menus.get(0), 1, "bi"));
        menus.add(MetaMenu.createEruptClassMenu(WxAddInfo.class, menus.get(0), 2));
        menus.add(MetaMenu.createEruptClassMenu(WxIdInfo.class, menus.get(0), 3));
        return menus;
    }

    @Override
    public void initFun() {
        iDaasService.register(WxfriendConst.APPID,
                WxfriendConst.APPNAME, PlatformType.DINGTALK_ACCOUNT,
                WxfriendConst.AK, WxfriendConst.SK,
                "http://ding-platform.yundingyun.net/sns/auth/" + WxfriendConst.APPID,
                "/#/build/" + WxAddInfo.class.getSimpleName());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dingPlatformService.registerDefaultAccessKey(WxfriendConst.AK, WxfriendConst.SK);
    }
}
