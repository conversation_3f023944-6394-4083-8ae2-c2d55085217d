package com.ydy.dingtalk.outVisit.config;

import xyz.erupt.annotation.config.Comment;

public interface OutVisitConst {

    /**
     * 应用ID，与模块ID相同
     */
    String APPID = "wx-outvisit";

    String APPNAME = "每日客户跟进";

    String AK = "dinglwd8oo1qzcptm0et";
    String SK = "XaFuuMnRno8570pzVqh5eYHSGyfBbD6WJgFChXVg6jW6hMyr_QPoGN-tPVbUaBuN";

    @Comment("销售角色")
    String ROLE_SALES = "sales";
    @Comment("管理角色")
    String ROLE_MANAGER = "manager";

    @Comment("菜单入口")
    String MENU_ROOT = "$" + APPID;

//    @Comment("统计菜单")
//    String MENU_STAT = "WxOutvisitStat";

    @Comment("统计菜单")
    String MENU_STAT = "/#/bi/WxOutvisitStat";
}
