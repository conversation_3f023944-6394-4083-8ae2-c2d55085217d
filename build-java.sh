DOCKER_NAME=ydy_ding_talk_gitlab

echo "正在打包...."${DOCKER_NAME}
docker stop ${DOCKER_NAME}
docker run -i --network=host --rm --name ${DOCKER_NAME} \
-e  JAVA_OPTS='-Xmx1024M -Xms1024M -Xmn256M -XX:MaxMetaspaceSize=64M -XX:MetaspaceSize=64M' \
-v "$PWD"/:/usr/src/mymaven \
-v "/home/<USER>/.m2":/root/.m2 \
-v "$PWD/../target:/usr/src/mymaven/target" \
-v "/home/<USER>/.m2/settings.xml":/usr/local/maven-mvnd-0.9.0-linux-amd64/conf/settings.xml \
-w /usr/src/mymaven \
mailbyms/mvnd:0.9.0-jdk-8 mvnd \
-e clean package -T 1C -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.clean.failOnError=false
