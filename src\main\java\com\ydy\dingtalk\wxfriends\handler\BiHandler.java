package com.ydy.dingtalk.wxfriends.handler;

import cn.hutool.core.util.StrUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import xyz.erupt.bi.fun.EruptBiHandler;
import xyz.erupt.core.util.EruptSpringUtil;
import xyz.erupt.toolkit.utils.EruptDaoUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@Service
public class BiHandler implements EruptBiHandler {

    @Override
    public String exprHandler(String param, Map<String, Object> condition, String expr) {
       return CommonHandler.exprHandler(param,condition,expr);
    }


}
