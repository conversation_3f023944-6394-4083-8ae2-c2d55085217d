package com.ydy.dingtalk.wxfriends.entity;

import cn.hutool.core.lang.RegexPool;
import com.ydy.dingtalk.wxfriends.handler.IsReadOnly;
import com.ydy.dingtalk.wxfriends.proxy.WxIdInfoDataProxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.db.Comment;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

@Erupt(
        name = "微信号管理",
        power = @Power(importable = false, export = false),
        importTruncate = true,
        dataProxy = WxIdInfoDataProxy.class
        , rowOperation = {})
@Table(name = "tb_wx_id")
@Entity
@Getter
@Setter
@Comment("微信号管理")
@ApiModel("微信号管理")
public class WxIdInfo extends MetaModel {


    @EruptField(views = @View(title = "团队"),
            edit = @Edit(
                    title = "团队",
                    type = EditType.CHOICE,
                    readonly = @Readonly(exprHandler = IsReadOnly.class),
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = "SELECT `id`,`name` FROM e_upms_org WHERE\n" +
                                    "parent_org_id IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t) OR `id` IN (\n" +
                                    "\t\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t)\n" +
                                    "\t\n"
                    )
            )
    )
    @Comment("团队")
    @ApiModelProperty("团队")
    private String subDeptId;

    @EruptField(views = @View(title = "使用人"),
            edit = @Edit(title = "使用人", type = EditType.CHOICE,
                    readonly = @Readonly(exprHandler = IsReadOnly.class),
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = "SELECT e_upms_user.`name`,e_upms_user.`name` FROM e_upms_user INNER JOIN\n" +
                                    "e_upms_org ON\te_upms_user.erupt_org_id=e_upms_org.id\n" +
                                    "WHERE e_upms_org.parent_org_id IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t) OR e_upms_org.`id` IN (\n" +
                                    "\tSELECT `id` FROM e_upms_org WHERE\n" +
                                    "\t\t`parent_org_id` = ( SELECT `id` FROM e_upms_org WHERE `name` = '营销中心' ) AND `name` NOT IN ('销售运营管理','政企大客户部','销售SaaS部')\n" +
                                    "\t)"
                    )
            )
    )
    @Comment("使用人")
    @ApiModelProperty("使用人")
    private String currUser;

    @EruptField(views = @View(title = "昵称"),
            edit = @Edit(title = "昵称", type = EditType.INPUT,search = @Search(vague = true), notNull = true, inputType = @InputType))
    @Comment("昵称")
    @ApiModelProperty("昵称")
    private String nickname;

    @EruptField(views = @View(title = "手机号"),
            edit = @Edit(title = "手机号", type = EditType.INPUT, search = @Search(vague = true),notNull = true, inputType = @InputType(regex = RegexPool.MOBILE)))
    @Comment("手机号")
    @ApiModelProperty("手机号")
    private String mobile;

    @EruptField(views = @View(title = "微信号"),
            edit = @Edit(title = "微信号", type = EditType.INPUT, notNull = true,search = @Search(vague = true), inputType = @InputType))
    @Comment("微信号")
    @ApiModelProperty("微信号")
    private String wechatId;

}
