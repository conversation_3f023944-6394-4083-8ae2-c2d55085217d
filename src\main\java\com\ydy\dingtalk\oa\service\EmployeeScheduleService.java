package com.ydy.dingtalk.oa.service;

import com.ydy.dingtalk.oa.entity.EmployeeScheduleRequest;
import com.ydy.dingtalk.oa.entity.EmployeeScheduleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Service for handling employee schedules
 */
@Slf4j
@Service
public class EmployeeScheduleService {

    @Resource
    private EruptDao eruptDao;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Pattern NAME_PATTERN = Pattern.compile("([^（]+)（[^）]+）");

    /**
     * Get employee schedules based on request
     *
     * @param request The request containing filter criteria
     * @return Employee schedule response
     */
    public EmployeeScheduleResponse getEmployeeSchedules(EmployeeScheduleRequest request) {
        // Set default date range to current week if not specified
        String startDate = request.getStartDate();
        String endDate = request.getEndDate();

        if (startDate == null || endDate == null) {
            LocalDate today = LocalDate.now();
            LocalDate monday = today.with(DayOfWeek.MONDAY);
            LocalDate sunday = today.with(DayOfWeek.SUNDAY);

            startDate = monday.format(DATE_FORMATTER);
            endDate = sunday.format(DATE_FORMATTER);
        }

        // Generate list of dates between start and end date
        List<String> dateRange = generateDateRange(startDate, endDate);

        // Get all employee names if not specified
        List<String> employeeNames = request.getEmployees();
        if (employeeNames == null || employeeNames.isEmpty()) {
            employeeNames = getAllEmployeeNames();
        }

        // Prepare response object
        EmployeeScheduleResponse response = new EmployeeScheduleResponse();
        response.setDates(dateRange);

        // Get schedule data for each employee
        List<EmployeeScheduleResponse.EmployeeSchedule> schedulesList = new ArrayList<>();
        for (String employeeName : employeeNames) {
            EmployeeScheduleResponse.EmployeeSchedule schedule = new EmployeeScheduleResponse.EmployeeSchedule();
            schedule.setEmployeeName(employeeName);

            Map<String, List<EmployeeScheduleResponse.ScheduleItem>> dateSchedules = new HashMap<>();
            for (String date : dateRange) {
                dateSchedules.put(date, getScheduleItemsForDate(employeeName, date));
            }

            schedule.setDateSchedules(dateSchedules);
            schedulesList.add(schedule);
        }

        response.setSchedules(schedulesList);
        return response;
    }

    /**
     * Get all employee names
     *
     * @return List of employee names
     */
    public List<String> getAllEmployeeNames() {
        Set<String> employeeNames = new HashSet<>();

        // Find the IDs of organizations named '销售一部', '销售二部', '销售三部'
        Query salesDeptQuery = eruptDao.getEntityManager().createNativeQuery(
                "SELECT id FROM e_upms_org WHERE name IN ('销售一部', '销售二部', '销售三部','电销助理部')");
        List<Number> salesDeptIds = salesDeptQuery.getResultList();

        if (salesDeptIds != null && !salesDeptIds.isEmpty()) {
            // Convert List<Number> to List<Long>
            List<Long> orgIds = new ArrayList<>();
            for (Number id : salesDeptIds) {
                orgIds.add(id.longValue());
            }

            // Find all child organizations recursively
            Set<Long> allOrgIds = new HashSet<>(orgIds);
            for (Long orgId : orgIds) {
                findChildOrgs(orgId, allOrgIds);
            }

            // Convert Set<Long> to comma-separated string for SQL IN clause
            StringBuilder orgIdStr = new StringBuilder();
            for (Long id : allOrgIds) {
                if (orgIdStr.length() > 0) {
                    orgIdStr.append(",");
                }
                orgIdStr.append(id);
            }

            // Get user names from those organizations
            if (orgIdStr.length() > 0) {
                Query userQuery = eruptDao.getEntityManager().createNativeQuery(
                                "SELECT name FROM e_upms_user WHERE erupt_org_id IN (" + orgIdStr.toString() + ") AND status = true")
                        .setMaxResults(1000); // Limit to 1000 users for performance

                List<String> users = userQuery.getResultList();
                employeeNames.addAll(users);
            }
        }

        return new ArrayList<>(employeeNames);
    }

    /**
     * Recursively find all child organizations of the given parent organization
     *
     * @param parentOrgId Parent organization ID
     * @param allOrgIds Set to collect all organization IDs
     */
    private void findChildOrgs(Long parentOrgId, Set<Long> allOrgIds) {
        Query childQuery = eruptDao.getEntityManager().createNativeQuery(
                        "SELECT id FROM e_upms_org WHERE parent_org_id = :parentId")
                .setParameter("parentId", parentOrgId);

        List<Number> childIds = childQuery.getResultList();
        if (childIds != null && !childIds.isEmpty()) {
            for (Number childId : childIds) {
                Long id = childId.longValue();
                if (allOrgIds.add(id)) { // Only process if this ID is new
                    findChildOrgs(id, allOrgIds); // Recursive call
                }
            }
        }
    }

    /**
     * Get schedule items for a specific employee and date
     *
     * @param employeeName Employee name
     * @param date Date in format yyyy-MM-dd
     * @return List of schedule items
     */
    private List<EmployeeScheduleResponse.ScheduleItem> getScheduleItemsForDate(String employeeName, String date) {
        List<EmployeeScheduleResponse.ScheduleItem> items = new ArrayList<>();

        // 1. Get external visits from tb_oa_info
        Query externalVisitsQuery = eruptDao.getEntityManager().createNativeQuery(
                        "SELECT start_time, end_time, company_name, company_address, multiple_companies, oa_type,destination " +
                                "FROM tb_oa_info WHERE submitter = :employee AND oa_type IN ('外出', '出差') " +
                                "AND DATE(start_time) <= :date AND DATE(end_time) >= :date")
                .setParameter("employee", employeeName)
                .setParameter("date", date);

        List<Object[]> externalVisits = externalVisitsQuery.getResultList();

        for (Object[] visit : externalVisits) {
            String startTime = (String) visit[0];
            String endTime = (String) visit[1];
            String companyName = visit[2] != null ? (String) visit[2] : "";
            String companyAddress = visit[3] != null ? (String) visit[3] : "";
            String multipleCompaniesJson = visit[4] != null ? (String) visit[4] : null;
            String oaType = (String) visit[5];
            String destination = visit[6] != null ? (String) visit[6] : "";

            // 根据类型选择显示内容：出差显示地址，外出显示公司名
            String displayContent = "出差".equals(oaType) ? destination : companyName;

            // Get company name from multiple_companies JSON if available
            if (multipleCompaniesJson != null && !multipleCompaniesJson.isEmpty()) {
                try {
                    // Parse the JSON string to extract company name
                    if (multipleCompaniesJson.contains("\"name\"")) {
                        int nameIndex = multipleCompaniesJson.indexOf("\"name\"");
                        int colonIndex = multipleCompaniesJson.indexOf(":", nameIndex);
                        int startIndex = multipleCompaniesJson.indexOf("\"", colonIndex) + 1;
                        int endIndex = multipleCompaniesJson.indexOf("\"", startIndex);
                        if (startIndex > 0 && endIndex > startIndex) {
                            companyName = multipleCompaniesJson.substring(startIndex, endIndex);
                            // 如果是出差类型，尝试获取地址
                            if ("出差".equals(oaType) && multipleCompaniesJson.contains("\"address\"")) {
                                int addrIndex = multipleCompaniesJson.indexOf("\"address\"");
                                int addrColonIndex = multipleCompaniesJson.indexOf(":", addrIndex);
                                int addrStartIndex = multipleCompaniesJson.indexOf("\"", addrColonIndex) + 1;
                                int addrEndIndex = multipleCompaniesJson.indexOf("\"", addrStartIndex);
                                if (addrStartIndex > 0 && addrEndIndex > addrStartIndex) {
                                    companyAddress = multipleCompaniesJson.substring(addrStartIndex, addrEndIndex);
                                    displayContent = companyAddress;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("Error parsing multiple_companies JSON: {}", e.getMessage());
                }
            }

            // Add morning or afternoon schedule items based on time
            LocalDate visitDate = LocalDate.parse(date, DATE_FORMATTER);
            LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), DATE_FORMATTER);

            // For multi-day events, determine which part of the day to show
            if (startDate.equals(visitDate) && endDate.equals(visitDate)) {
                // Single day event
                int startHour = Integer.parseInt(startTime.substring(11, 13));
                int endHour = Integer.parseInt(endTime.substring(11, 13));

                // 新的时间规则：9-12点算上午，13-18点算下午，9-18点显示全天
                if (startHour >= 9 && endHour <= 12) {
                    // 上午
                    addScheduleItem(items, oaType, "上午", displayContent);
                } else if (startHour >= 13 && endHour <= 18) {
                    // 下午
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else if (startHour >= 9 && endHour <= 18) {
                    // 全天
                    addScheduleItem(items, oaType, "上午", displayContent);
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else if (startHour < 9) {
                    // 早于9点的安排，显示在上午
                    addScheduleItem(items, oaType, "上午", displayContent);
                } else if (startHour > 18) {
                    // 晚于18点的安排，显示在下午
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else {
                    // 其他情况，根据结束时间判断
                    if (endHour <= 12) {
                        addScheduleItem(items, oaType, "上午", displayContent);
                    } else {
                        addScheduleItem(items, oaType, "下午", displayContent);
                    }
                }
            } else if (startDate.equals(visitDate)) {
                // First day of multi-day event
                int startHour = Integer.parseInt(startTime.substring(11, 13));
                if (startHour >= 9 && startHour <= 12) {
                    // 从上午开始
                    addScheduleItem(items, oaType, "上午", displayContent);
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else if (startHour >= 13 && startHour <= 18) {
                    // 从下午开始
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else if (startHour < 9) {
                    // 早于9点开始
                    addScheduleItem(items, oaType, "上午", displayContent);
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else {
                    // 其他情况，显示在下午
                    addScheduleItem(items, oaType, "下午", displayContent);
                }
            } else if (endDate.equals(visitDate)) {
                // Last day of multi-day event
                int endHour = Integer.parseInt(endTime.substring(11, 13));
                if (endHour >= 13 && endHour <= 18) {
                    // 在下午结束
                    addScheduleItem(items, oaType, "上午", displayContent);
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else if (endHour >= 9 && endHour <= 12) {
                    // 在上午结束
                    addScheduleItem(items, oaType, "上午", displayContent);
                } else if (endHour > 18) {
                    // 晚于18点结束
                    addScheduleItem(items, oaType, "上午", displayContent);
                    addScheduleItem(items, oaType, "下午", displayContent);
                } else {
                    // 早于9点结束
                    addScheduleItem(items, oaType, "上午", displayContent);
                }
            } else if (visitDate.isAfter(startDate) && visitDate.isBefore(endDate)) {
                // Middle day of multi-day event, show all day
                addScheduleItem(items, oaType, "上午", displayContent);
                addScheduleItem(items, oaType, "下午", displayContent);
            }
        }

        // 2. Get calendar events from tb_oa_calendar_info
        Query calendarQuery = eruptDao.getEntityManager().createNativeQuery(
                        "SELECT start_time, end_time, subject, position, sign " +
                                "FROM tb_oa_calendar_info WHERE oa_type = '日程' " +
                                "AND DATE(start_time) <= :date AND DATE(end_time) >= :date " +
                                "AND participants LIKE :employeePattern")
                .setParameter("date", date)
                .setParameter("employeePattern", "%" + employeeName + "%");

        List<Object[]> calendarEvents = calendarQuery.getResultList();

        for (Object[] event : calendarEvents) {
            String startTime = (String) event[0];
            String endTime = (String) event[1];
            String subject = event[2] != null ? (String) event[2] : "";
            String sign = event[4] != null ? (String) event[4] : null;

            if (sign == null) continue;

            // Add calendar events based on sign (tag)
            if ("培训交流".equals(sign) || "外部参会".equals(sign) || "内部会议".equals(sign)) {
                LocalDate eventDate = LocalDate.parse(date, DATE_FORMATTER);
                LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), DATE_FORMATTER);
                LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), DATE_FORMATTER);

                // 使用新的时间规则
                if (startDate.equals(eventDate) && endDate.equals(eventDate)) {
                    int startHour = Integer.parseInt(startTime.substring(11, 13));
                    int endHour = Integer.parseInt(endTime.substring(11, 13));

                    if (startHour >= 9 && endHour <= 12) {
                        // 上午
                        addScheduleItem(items, sign, "上午", subject);
                    } else if (startHour >= 13 && endHour <= 18) {
                        // 下午
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (startHour >= 9 && endHour <= 18) {
                        // 全天
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (startHour < 9) {
                        // 早于9点开始
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (startHour > 18) {
                        // 晚于18点开始
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else {
                        // 其他情况，根据结束时间判断
                        if (endHour <= 12) {
                            addScheduleItem(items, sign, "上午", subject);
                        } else {
                            addScheduleItem(items, sign, "下午", subject);
                        }
                    }
                } else if (startDate.equals(eventDate)) {
                    int startHour = Integer.parseInt(startTime.substring(11, 13));
                    if (startHour >= 9 && startHour <= 12) {
                        // 从上午开始
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (startHour >= 13 && startHour <= 18) {
                        // 从下午开始
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (startHour < 9) {
                        // 早于9点开始
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else {
                        // 其他情况，显示在下午
                        addScheduleItem(items, sign, "下午", subject);
                    }
                } else if (endDate.equals(eventDate)) {
                    int endHour = Integer.parseInt(endTime.substring(11, 13));
                    if (endHour >= 13 && endHour <= 18) {
                        // 在下午结束
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else if (endHour >= 9 && endHour <= 12) {
                        // 在上午结束
                        addScheduleItem(items, sign, "上午", subject);
                    } else if (endHour > 18) {
                        // 晚于18点结束
                        addScheduleItem(items, sign, "上午", subject);
                        addScheduleItem(items, sign, "下午", subject);
                    } else {
                        // 早于9点结束
                        addScheduleItem(items, sign, "上午", subject);
                    }
                } else if (eventDate.isAfter(startDate) && eventDate.isBefore(endDate)) {
                    // 中间日期显示全天
                    addScheduleItem(items, sign, "上午", subject);
                    addScheduleItem(items, sign, "下午", subject);
                }
            }
        }

        return items;
    }

    /**
     * Helper method to add a schedule item to the list
     */
    private void addScheduleItem(List<EmployeeScheduleResponse.ScheduleItem> items, String type, String timePeriod, String content) {
        // 检查是否已经存在相同时间段和相同公司名称的记录
        for (EmployeeScheduleResponse.ScheduleItem existingItem : items) {
            if (existingItem.getTimePeriod().equals(timePeriod) && existingItem.getContent().equals(content)) {
                // 已存在相同时间段和公司的记录，不再添加
                return;
            }
        }

        // 不存在重复记录，添加新记录
        EmployeeScheduleResponse.ScheduleItem item = new EmployeeScheduleResponse.ScheduleItem();
        item.setType(type);
        item.setTimePeriod(timePeriod);
        item.setContent(content);
        items.add(item);
    }

    /**
     * Generate a list of date strings between start and end dates
     *
     * @param startDate Start date in format yyyy-MM-dd
     * @param endDate End date in format yyyy-MM-dd
     * @return List of date strings
     */
    private List<String> generateDateRange(String startDate, String endDate) {
        List<String> dateRange = new ArrayList<>();

        LocalDate start = LocalDate.parse(startDate, DATE_FORMATTER);
        LocalDate end = LocalDate.parse(endDate, DATE_FORMATTER);

        LocalDate current = start;
        while (!current.isAfter(end)) {
            dateRange.add(current.format(DATE_FORMATTER));
            current = current.plusDays(1);
        }

        return dateRange;
    }
}
