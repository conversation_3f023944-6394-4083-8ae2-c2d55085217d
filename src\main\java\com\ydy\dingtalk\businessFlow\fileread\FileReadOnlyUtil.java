package com.ydy.dingtalk.businessFlow.fileread;

import org.springframework.stereotype.Component;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import java.util.Set;

@Component
public class FileReadOnlyUtil implements Readonly.ReadonlyHandler {
    @Resource
    private EruptUserService eruptUserService;

    @Override
    public boolean add(boolean add, String[] params) {
        return false;
    }

    @Override
    public boolean edit(boolean edit, String[] params) {
        Set<EruptRole> roles = eruptUserService.getCurrentEruptUser().getRoles();
        boolean res = true;
        for (EruptRole role : roles) {
            if(role.getName().equals("商机流转管理-电销")){
                res = false;
            }
        }
        return res;
    }
}
