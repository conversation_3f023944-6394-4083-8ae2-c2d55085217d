package com.ydy.dingtalk.businessFlow.util;

import cn.hutool.core.date.DateUtil;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.ydy.dingtalk.businessFlow.config.BusinessFlowConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import xyz.erupt.dingtalk.api.UserAPI;
import xyz.erupt.idaas.entity.AccessKey;
import xyz.erupt.idaas.entity.IdaasUser;
import xyz.erupt.idaas.service.AccessKeyService;
import xyz.erupt.toolkit.utils.EruptDaoUtils;
import xyz.erupt.toolkit.utils.NotifyUtils;

import javax.annotation.Resource;

/**
 * 钉钉发送消息工具类
 */
@Component
@Slf4j
public class DingTalkSendMsgUtil {

    @Resource
    private AccessKeyService accessKeyService;

    @Value("${AGENT_ID}")
    private String AGENT_ID;

    /**
     * 商机流转发送消息给面销人员
     *
     * @param name 面销人员名称
     */
    public void businessFlowConstSendMsgToLineDown(String name, String companyName) {
        // 获取accessKey，借以获取token
        AccessKey accessKey = accessKeyService.getAccessKey_(BusinessFlowConst.APPID);
        // 判断是否包含token
        if (accessKey == null || accessKey.getToken() == null) {
            log.error("缺少token，给接单人发送钉钉消息通知失败！");
            NotifyUtils.showErrorDialog("缺少关键信息，给接单人发送钉钉消息通知失败！");
            return;
        }
        // 获取用户信息，借以获取platformUserId
        IdaasUser idaasUser = EruptDaoUtils.getEruptDao().queryEntity(IdaasUser.class, "platformUserName='" + name + "'");
        // 判断是否包含platformUserId
        if (idaasUser == null || idaasUser.getPlatformUserId() == null) {
            log.error("未找到platformUserId，给接单人发送钉钉消息通知失败！");
            NotifyUtils.showErrorDialog("缺少关键信息，给接单人发送钉钉消息通知失败！");
            return;
        }
        // 发送消息具体内容
        String msg = "【" + companyName + "】的商机已于【" + DateUtil.now() + "】转交给你，请进入商机系统查看~";
        // 调用钉钉api发送消息给面销
        OapiMessageCorpconversationAsyncsendV2Response response = UserAPI.sendMessage(accessKey.getToken(), Long.parseLong(AGENT_ID), msg, idaasUser.getPlatformUserId());
        log.info("taskId：" + response.getTaskId());
    }
}
